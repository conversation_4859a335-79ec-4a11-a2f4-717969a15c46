# Kh<PERSON><PERSON> phục lỗi Spring Security Exception

## Vấn đề
```
jakarta.servlet.ServletException: Unable to handle the Spring Security Exception because the response is already committed.
Caused by: org.springframework.security.access.AccessDeniedException: Access Denied
```

## Nguyên nhân
1. **Response đã được commit**: HTTP response đã được gửi về client trước khi Spring Security có thể xử lý exception
2. **Thiếu Exception Handlers**: Không có custom handlers để xử lý `AccessDeniedException` và `AuthenticationException`
3. **Cấu hình Security không đầy đủ**: Thiếu `exceptionHandling` configuration

## Giải pháp đã thực hiện

### 1. Tạo Global Exception Handler
✅ **File**: `src/main/java/com/smartscheduler/exception/GlobalExceptionHandler.java`
- <PERSON><PERSON> lý `AccessDeniedException`
- <PERSON><PERSON> lý `AuthenticationException`
- <PERSON><PERSON> lý validation errors
- Kiểm tra response committed status

### 2. Tạo JWT Exception Handlers
✅ **File**: `src/main/java/com/smartscheduler/security/JwtAuthenticationEntryPoint.java`
- Xử lý authentication failures
- Trả về JSON response thay vì HTML error page

✅ **File**: `src/main/java/com/smartscheduler/security/JwtAccessDeniedHandler.java`
- Xử lý access denied exceptions
- Kiểm tra response committed status
- Trả về JSON response nhất quán

### 3. Tạo Custom Exception Class
✅ **File**: `src/main/java/com/smartscheduler/exception/ResourceNotFoundException.java`
- Custom exception cho resource not found scenarios

### 4. Cập nhật Security Configuration
✅ **File**: `src/main/java/com/smartscheduler/config/SecurityConfig.java`
- Thêm `exceptionHandling` configuration
- Sử dụng custom authentication entry point và access denied handler
- Cải thiện CORS configuration
- Thêm `/error` endpoint vào permitAll

### 5. Cải thiện JWT Authentication Filter
✅ **File**: `src/main/java/com/smartscheduler/security/JwtAuthenticationFilter.java`
- Clear security context khi có exception
- Tránh gây ra response committed issues

## Cấu hình mới

### Security Filter Chain
```java
@Bean
public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
    http
        .cors(cors -> cors.configurationSource(corsConfigurationSource()))
        .csrf(csrf -> csrf.disable())
        .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        .exceptionHandling(exceptions -> exceptions
            .authenticationEntryPoint(jwtAuthenticationEntryPoint())
            .accessDeniedHandler(jwtAccessDeniedHandler())
        )
        .authorizeHttpRequests(authz -> authz
            .requestMatchers("/auth/**").permitAll()
            .requestMatchers(HttpMethod.GET, "/departments/**", "/majors/**", "/courses/**").permitAll()
            .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
            .requestMatchers("/error").permitAll()
            .anyRequest().authenticated()
        );

    http.authenticationProvider(authenticationProvider());
    http.addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
    
    return http.build();
}
```

### Exception Response Format
```json
{
  "status": 403,
  "error": "Forbidden",
  "message": "Access denied. You don't have permission to access this resource.",
  "path": "/api/schedules",
  "timestamp": 1703123456789
}
```

## Kiểm tra sau khi khắc phục

1. **Restart ứng dụng**:
```bash
mvn spring-boot:run
```

2. **Test authentication**:
```bash
# Login
curl -X POST http://localhost:8080/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# Access protected resource without token
curl -X GET http://localhost:8080/api/schedules
```

3. **Kiểm tra logs**: Không còn thấy ServletException về response committed

## Lợi ích

1. **Consistent Error Responses**: Tất cả errors đều trả về JSON format
2. **Better Security**: Proper handling của authentication và authorization errors
3. **No More Response Committed**: Tránh được lỗi response đã committed
4. **Better Debugging**: Clear error messages và logging
5. **Frontend Friendly**: JSON responses dễ xử lý ở frontend

## Lưu ý

- Exception handlers có thứ tự ưu tiên: Security handlers → Global handlers
- Response committed check để tránh IllegalStateException
- CORS configuration cho phép frontend access
- Logging để debug và monitoring

## Files đã tạo/cập nhật

```
src/main/java/com/smartscheduler/
├── exception/
│   ├── GlobalExceptionHandler.java          ✅ NEW
│   └── ResourceNotFoundException.java       ✅ NEW
├── security/
│   ├── JwtAuthenticationEntryPoint.java     ✅ NEW
│   ├── JwtAccessDeniedHandler.java          ✅ NEW
│   └── JwtAuthenticationFilter.java         ✅ UPDATED
└── config/
    └── SecurityConfig.java                  ✅ UPDATED
```

Sau khi áp dụng các thay đổi này, lỗi Spring Security Exception sẽ được khắc phục hoàn toàn!
