@echo off
echo ========================================
echo Fixing Remaining Controllers
echo ========================================

echo.
echo Setting up Java 17 environment...
set "JAVA_HOME=C:\Program Files\Java\jdk-17"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo.
echo Step 1: Testing current build status...
mvn compile -q

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ALL CONTROLLERS FIXED SUCCESSFULLY!
    echo ========================================
    echo.
    echo The build is now working. All ResponseEntity issues have been resolved.
    echo You can now run the application with: mvn spring-boot:run
) else (
    echo.
    echo Some controllers still need fixing...
    echo.
    echo The following controllers have been fixed:
    echo - ClassController.java
    echo - TeacherController.java  
    echo - CampusController.java
    echo - DepartmentController.java
    echo.
    echo Remaining controllers that may need fixing:
    echo - RoomController.java
    echo - MajorController.java
    echo - CourseController.java
    echo - ScheduleController.java
    echo - TeacherWorkloadController.java
    echo.
    echo Also need to fix ScheduleService.java Class name conflicts.
)

echo.
echo Press any key to continue...
pause > nul
