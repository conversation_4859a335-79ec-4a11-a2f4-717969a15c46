package com.smartscheduler.model;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;

@Entity
@Table(name = "teacher_workloads")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TeacherWorkload extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "teacher_id", nullable = false)
    private Teacher teacher;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "course_id", nullable = false)
    private Course course;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "class_id", nullable = false)
    private Class classEntity;
    
    @Column(name = "academic_year", nullable = false)
    private String academicYear;
    
    @Column(name = "semester")
    private Integer semester;
    
    @Column(name = "teaching_date", nullable = false)
    private LocalDate teachingDate;
    
    @Column(name = "hours", nullable = false)
    private Integer hours;
    
    @Column(name = "coefficient", nullable = false)
    private Double coefficient;
    
    @Column(name = "weighted_hours", nullable = false)
    private Double weightedHours;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "teaching_format", nullable = false)
    private Course.TeachingFormat teachingFormat;
    
    @PrePersist
    @PreUpdate
    public void calculateWeightedHours() {
        this.weightedHours = this.hours * this.coefficient;
    }
}
