package com.smartscheduler.model;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "academic_weeks")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AcademicWeek extends BaseEntity {

    @Column(name = "week_number", nullable = false)
    private Integer weekNumber;
    
    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;
    
    @Column(name = "end_date", nullable = false)
    private LocalDate endDate;
    
    @Column(name = "academic_year")
    private String academicYear;
    
    @Column(name = "semester")
    private Integer semester;
    
    @OneToMany(mappedBy = "academicWeek", cascade = CascadeType.ALL)
    private Set<Schedule> schedules = new HashSet<>();
}
