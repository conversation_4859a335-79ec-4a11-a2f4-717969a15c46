package com.smartscheduler.model;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "time_slots")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TimeSlot extends BaseEntity {

    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "start_time", nullable = false)
    private LocalTime startTime;
    
    @Column(name = "end_time", nullable = false)
    private LocalTime endTime;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "session", nullable = false)
    private Session session;
    
    @OneToMany(mappedBy = "timeSlot", cascade = CascadeType.ALL)
    private Set<Schedule> schedules = new HashSet<>();
    
    public enum Session {
        MORNING, AFTERNOON, EVENING
    }
}
