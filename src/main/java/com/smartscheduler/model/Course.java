package com.smartscheduler.model;

import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "courses")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Course extends BaseEntity {

    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "code", nullable = false, unique = true)
    private String code;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "credits")
    private Integer credits;
    
    @Column(name = "total_hours")
    private Integer totalHours;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "teaching_format")
    private TeachingFormat teachingFormat;
    
    @OneToMany(mappedBy = "course", cascade = CascadeType.ALL)
    private Set<Lesson> lessons = new HashSet<>();
    
    @OneToMany(mappedBy = "course", cascade = CascadeType.ALL)
    private Set<Schedule> schedules = new HashSet<>();
    
    public enum TeachingFormat {
        THEORY, PRACTICE
    }
}
