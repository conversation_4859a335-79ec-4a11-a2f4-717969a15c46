package com.smartscheduler.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "lessons")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Lesson extends BaseEntity {

    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "sequence_number")
    private Integer sequenceNumber;
    
    @Column(name = "hours")
    private Integer hours;
    
    @Column(name = "coefficient")
    private Double coefficient;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "course_id", nullable = false)
    private Course course;
}
