package com.smartscheduler.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "schedules")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Schedule extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "teacher_id", nullable = false)
    private Teacher teacher;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "course_id", nullable = false)
    private Course course;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "lesson_id")
    private Lesson lesson;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "class_id", nullable = false)
    private Class classEntity;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "room_id", nullable = false)
    private Room room;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "time_slot_id", nullable = false)
    private TimeSlot timeSlot;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "week_day_id", nullable = false)
    private WeekDay weekDay;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "academic_week_id", nullable = false)
    private AcademicWeek academicWeek;
    
    @Column(name = "practice_group")
    private Integer practiceGroup;
    
    @Column(name = "hours")
    private Integer hours;
    
    @Column(name = "coefficient")
    private Double coefficient;
    
    @Column(name = "notes")
    private String notes;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "teaching_format", nullable = false)
    private Course.TeachingFormat teachingFormat;
}
