package com.smartscheduler.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "week_days")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WeekDay extends BaseEntity {

    @Column(name = "name", nullable = false, unique = true)
    private String name;

    @Column(name = "day_order", nullable = false, unique = true)
    private Integer dayOrder;

    @JsonIgnore
    @OneToMany(mappedBy = "weekDay", cascade = CascadeType.ALL)
    private Set<Schedule> schedules = new HashSet<>();
}
