package com.smartscheduler.model;

import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "classes")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Class extends BaseEntity {

    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "code", nullable = false, unique = true)
    private String code;
    
    @Column(name = "year")
    private Integer year;
    
    @Column(name = "semester")
    private Integer semester;
    
    @Column(name = "student_count")
    private Integer studentCount;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "major_id", nullable = false)
    private Major major;
    
    @OneToMany(mappedBy = "classEntity", cascade = CascadeType.ALL)
    private Set<Schedule> schedules = new HashSet<>();
}
