package com.smartscheduler.security;

import com.smartscheduler.model.User;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

@Component("userSecurity")
public class UserSecurity {

    public boolean isTeacherOrAdmin(Long teacherId, User user) {
        // Check if user is an admin
        boolean isAdmin = user.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(role -> role.equals("ROLE_ADMIN"));
        
        if (isAdmin) {
            return true;
        }
        
        // Check if user is the teacher
        if (user.getTeacher() != null) {
            return user.getTeacher().getId().equals(teacherId);
        }
        
        return false;
    }
}
