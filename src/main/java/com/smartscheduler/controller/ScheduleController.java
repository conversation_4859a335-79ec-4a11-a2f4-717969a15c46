package com.smartscheduler.controller;

import com.smartscheduler.dto.MessageResponse;
import com.smartscheduler.model.Schedule;
import com.smartscheduler.service.ScheduleService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/schedules")
public class ScheduleController {

    @Autowired
    private ScheduleService scheduleService;

    @GetMapping
    public ResponseEntity<List<Schedule>> getAllSchedules() {
        List<Schedule> schedules = scheduleService.getAllSchedules();
        return new ResponseEntity<>(schedules, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getScheduleById(@PathVariable Long id) {
        return scheduleService.getScheduleById(id)
                .<ResponseEntity<?>>map(schedule -> ResponseEntity.ok(schedule))
                .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new MessageResponse("Schedule not found with id: " + id)));
    }

    @GetMapping("/teacher/{teacherId}")
    public ResponseEntity<List<Schedule>> getSchedulesByTeacher(@PathVariable Long teacherId) {
        List<Schedule> schedules = scheduleService.getSchedulesByTeacher(teacherId);
        return new ResponseEntity<>(schedules, HttpStatus.OK);
    }

    @GetMapping("/teacher/{teacherId}/week/{weekId}")
    public ResponseEntity<List<Schedule>> getSchedulesByTeacherAndWeek(
            @PathVariable Long teacherId,
            @PathVariable Long weekId) {
        List<Schedule> schedules = scheduleService.getSchedulesByTeacherAndWeek(teacherId, weekId);
        return new ResponseEntity<>(schedules, HttpStatus.OK);
    }

    @GetMapping("/class/{classId}")
    public ResponseEntity<List<Schedule>> getSchedulesByClass(@PathVariable Long classId) {
        List<Schedule> schedules = scheduleService.getSchedulesByClass(classId);
        return new ResponseEntity<>(schedules, HttpStatus.OK);
    }

    @GetMapping("/class/{classId}/week/{weekId}")
    public ResponseEntity<List<Schedule>> getSchedulesByClassAndWeek(
            @PathVariable Long classId,
            @PathVariable Long weekId) {
        List<Schedule> schedules = scheduleService.getSchedulesByClassAndWeek(classId, weekId);
        return new ResponseEntity<>(schedules, HttpStatus.OK);
    }

    @GetMapping("/room/{roomId}")
    public ResponseEntity<List<Schedule>> getSchedulesByRoom(@PathVariable Long roomId) {
        List<Schedule> schedules = scheduleService.getSchedulesByRoom(roomId);
        return new ResponseEntity<>(schedules, HttpStatus.OK);
    }

    @GetMapping("/room/{roomId}/week/{weekId}")
    public ResponseEntity<List<Schedule>> getSchedulesByRoomAndWeek(
            @PathVariable Long roomId,
            @PathVariable Long weekId) {
        List<Schedule> schedules = scheduleService.getSchedulesByRoomAndWeek(roomId, weekId);
        return new ResponseEntity<>(schedules, HttpStatus.OK);
    }

    @GetMapping("/week/{weekId}")
    public ResponseEntity<List<Schedule>> getSchedulesByWeek(@PathVariable Long weekId) {
        List<Schedule> schedules = scheduleService.getSchedulesByWeek(weekId);
        return new ResponseEntity<>(schedules, HttpStatus.OK);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createSchedule(@Valid @RequestBody Schedule schedule) {
        try {
            Schedule createdSchedule = scheduleService.createSchedule(schedule);
            return new ResponseEntity<>(createdSchedule, HttpStatus.CREATED);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateSchedule(@PathVariable Long id, @Valid @RequestBody Schedule schedule) {
        try {
            Schedule updatedSchedule = scheduleService.updateSchedule(id, schedule);
            return new ResponseEntity<>(updatedSchedule, HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()),
                    e.getMessage().contains("not found") ? HttpStatus.NOT_FOUND : HttpStatus.BAD_REQUEST);
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteSchedule(@PathVariable Long id) {
        try {
            scheduleService.deleteSchedule(id);
            return new ResponseEntity<>(new MessageResponse("Schedule deleted successfully"), HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }
}
