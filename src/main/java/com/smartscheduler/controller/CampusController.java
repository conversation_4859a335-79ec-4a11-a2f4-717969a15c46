package com.smartscheduler.controller;

import com.smartscheduler.dto.MessageResponse;
import com.smartscheduler.model.Campus;
import com.smartscheduler.service.CampusService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/campuses")
public class CampusController {

    @Autowired
    private CampusService campusService;

    @GetMapping
    public ResponseEntity<List<Campus>> getAllCampuses() {
        List<Campus> campuses = campusService.getAllCampuses();
        return ResponseEntity.ok(campuses);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getCampusById(@PathVariable Long id) {
        return campusService.getCampusById(id)
                .<ResponseEntity<?>>map(campus -> ResponseEntity.ok(campus))
                .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new MessageResponse("Campus not found with id: " + id)));
    }

    @GetMapping("/code/{code}")
    public ResponseEntity<?> getCampusByCode(@PathVariable String code) {
        return campusService.getCampusByCode(code)
                .<ResponseEntity<?>>map(campus -> ResponseEntity.ok(campus))
                .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new MessageResponse("Campus not found with code: " + code)));
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createCampus(@Valid @RequestBody Campus campus) {
        if (campusService.existsByCode(campus.getCode())) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: Campus code is already taken!"));
        }

        Campus createdCampus = campusService.createCampus(campus);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdCampus);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateCampus(@PathVariable Long id, @Valid @RequestBody Campus campus) {
        try {
            Campus updatedCampus = campusService.updateCampus(id, campus);
            return ResponseEntity.ok(updatedCampus);
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(new MessageResponse(e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteCampus(@PathVariable Long id) {
        try {
            campusService.deleteCampus(id);
            return ResponseEntity.ok(new MessageResponse("Campus deleted successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(new MessageResponse(e.getMessage()));
        }
    }
}
