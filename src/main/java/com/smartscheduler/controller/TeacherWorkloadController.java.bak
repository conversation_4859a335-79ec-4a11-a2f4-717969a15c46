package com.smartscheduler.controller;

import com.smartscheduler.dto.MessageResponse;
import com.smartscheduler.model.TeacherWorkload;
import com.smartscheduler.service.TeacherWorkloadService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/workloads")
public class TeacherWorkloadController {

    @Autowired
    private TeacherWorkloadService workloadService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<List<TeacherWorkload>> getAllWorkloads() {
        List<TeacherWorkload> workloads = workloadService.getAllWorkloads();
        return new ResponseEntity<>(workloads, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<?> getWorkloadById(@PathVariable Long id) {
        return workloadService.getWorkloadById(id)
                .map(workload -> new ResponseEntity<>(workload, HttpStatus.OK))
                .orElse(new ResponseEntity<>(new MessageResponse("Workload not found with id: " + id), HttpStatus.NOT_FOUND));
    }
    
    @GetMapping("/teacher/{teacherId}")
    @PreAuthorize("hasRole('ADMIN') or @userSecurity.isTeacherOrAdmin(#teacherId, principal)")
    public ResponseEntity<List<TeacherWorkload>> getWorkloadsByTeacher(@PathVariable Long teacherId) {
        List<TeacherWorkload> workloads = workloadService.getWorkloadsByTeacher(teacherId);
        return new ResponseEntity<>(workloads, HttpStatus.OK);
    }
    
    @GetMapping("/teacher/{teacherId}/academic-year/{academicYear}")
    @PreAuthorize("hasRole('ADMIN') or @userSecurity.isTeacherOrAdmin(#teacherId, principal)")
    public ResponseEntity<List<TeacherWorkload>> getWorkloadsByTeacherAndAcademicYear(
            @PathVariable Long teacherId, 
            @PathVariable String academicYear) {
        List<TeacherWorkload> workloads = workloadService.getWorkloadsByTeacherAndAcademicYear(teacherId, academicYear);
        return new ResponseEntity<>(workloads, HttpStatus.OK);
    }
    
    @GetMapping("/teacher/{teacherId}/date-range")
    @PreAuthorize("hasRole('ADMIN') or @userSecurity.isTeacherOrAdmin(#teacherId, principal)")
    public ResponseEntity<List<TeacherWorkload>> getWorkloadsByTeacherAndDateRange(
            @PathVariable Long teacherId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<TeacherWorkload> workloads = workloadService.getWorkloadsByTeacherAndDateRange(teacherId, startDate, endDate);
        return new ResponseEntity<>(workloads, HttpStatus.OK);
    }
    
    @GetMapping("/teacher/{teacherId}/total/academic-year/{academicYear}")
    @PreAuthorize("hasRole('ADMIN') or @userSecurity.isTeacherOrAdmin(#teacherId, principal)")
    public ResponseEntity<Map<String, Double>> getTotalWeightedHoursByTeacherAndAcademicYear(
            @PathVariable Long teacherId, 
            @PathVariable String academicYear) {
        Double total = workloadService.getTotalWeightedHoursByTeacherAndAcademicYear(teacherId, academicYear);
        return new ResponseEntity<>(Map.of("totalWeightedHours", total), HttpStatus.OK);
    }
    
    @GetMapping("/teacher/{teacherId}/total/date-range")
    @PreAuthorize("hasRole('ADMIN') or @userSecurity.isTeacherOrAdmin(#teacherId, principal)")
    public ResponseEntity<Map<String, Double>> getTotalWeightedHoursByTeacherAndDateRange(
            @PathVariable Long teacherId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        Double total = workloadService.getTotalWeightedHoursByTeacherAndDateRange(teacherId, startDate, endDate);
        return new ResponseEntity<>(Map.of("totalWeightedHours", total), HttpStatus.OK);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createWorkload(@Valid @RequestBody TeacherWorkload workload) {
        try {
            TeacherWorkload createdWorkload = workloadService.createWorkload(workload);
            return new ResponseEntity<>(createdWorkload, HttpStatus.CREATED);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.BAD_REQUEST);
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateWorkload(@PathVariable Long id, @Valid @RequestBody TeacherWorkload workload) {
        try {
            TeacherWorkload updatedWorkload = workloadService.updateWorkload(id, workload);
            return new ResponseEntity<>(updatedWorkload, HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteWorkload(@PathVariable Long id) {
        try {
            workloadService.deleteWorkload(id);
            return new ResponseEntity<>(new MessageResponse("Workload deleted successfully"), HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }
}
