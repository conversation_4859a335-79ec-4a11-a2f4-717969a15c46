package com.smartscheduler.controller;

import com.smartscheduler.dto.MessageResponse;
import com.smartscheduler.model.Class;
import com.smartscheduler.service.ClassService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/classes")
public class ClassController {

    @Autowired
    private ClassService classService;

    @GetMapping
    public ResponseEntity<List<Class>> getAllClasses() {
        List<Class> classes = classService.getAllClasses();
        return new ResponseEntity<>(classes, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getClassById(@PathVariable Long id) {
        return classService.getClassById(id)
                .map(classEntity -> new ResponseEntity<>(classEntity, HttpStatus.OK))
                .orElse(new ResponseEntity<>(new MessageResponse("Class not found with id: " + id), HttpStatus.NOT_FOUND));
    }

    @GetMapping("/code/{code}")
    public ResponseEntity<?> getClassByCode(@PathVariable String code) {
        return classService.getClassByCode(code)
                .map(classEntity -> new ResponseEntity<>(classEntity, HttpStatus.OK))
                .orElse(new ResponseEntity<>(new MessageResponse("Class not found with code: " + code), HttpStatus.NOT_FOUND));
    }
    
    @GetMapping("/major/{majorId}")
    public ResponseEntity<List<Class>> getClassesByMajor(@PathVariable Long majorId) {
        List<Class> classes = classService.getClassesByMajor(majorId);
        return new ResponseEntity<>(classes, HttpStatus.OK);
    }
    
    @GetMapping("/major/{majorId}/year/{year}")
    public ResponseEntity<List<Class>> getClassesByMajorAndYear(
            @PathVariable Long majorId, 
            @PathVariable Integer year) {
        List<Class> classes = classService.getClassesByMajorAndYear(majorId, year);
        return new ResponseEntity<>(classes, HttpStatus.OK);
    }
    
    @GetMapping("/major/{majorId}/year/{year}/semester/{semester}")
    public ResponseEntity<List<Class>> getClassesByMajorAndYearAndSemester(
            @PathVariable Long majorId, 
            @PathVariable Integer year,
            @PathVariable Integer semester) {
        List<Class> classes = classService.getClassesByMajorAndYearAndSemester(majorId, year, semester);
        return new ResponseEntity<>(classes, HttpStatus.OK);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createClass(@Valid @RequestBody Class classEntity) {
        if (classService.existsByCode(classEntity.getCode())) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: Class code is already taken!"));
        }
        
        Class createdClass = classService.createClass(classEntity);
        return new ResponseEntity<>(createdClass, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateClass(@PathVariable Long id, @Valid @RequestBody Class classEntity) {
        try {
            Class updatedClass = classService.updateClass(id, classEntity);
            return new ResponseEntity<>(updatedClass, HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteClass(@PathVariable Long id) {
        try {
            classService.deleteClass(id);
            return new ResponseEntity<>(new MessageResponse("Class deleted successfully"), HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }
}
