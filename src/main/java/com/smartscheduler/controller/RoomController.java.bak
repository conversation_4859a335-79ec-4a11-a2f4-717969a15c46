package com.smartscheduler.controller;

import com.smartscheduler.dto.MessageResponse;
import com.smartscheduler.model.Room;
import com.smartscheduler.service.RoomService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/rooms")
public class RoomController {

    @Autowired
    private RoomService roomService;

    @GetMapping
    public ResponseEntity<List<Room>> getAllRooms() {
        List<Room> rooms = roomService.getAllRooms();
        return new ResponseEntity<>(rooms, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getRoomById(@PathVariable Long id) {
        return roomService.getRoomById(id)
                .map(room -> new ResponseEntity<>(room, HttpStatus.OK))
                .orElse(new ResponseEntity<>(new MessageResponse("Room not found with id: " + id), HttpStatus.NOT_FOUND));
    }

    @GetMapping("/code/{code}")
    public ResponseEntity<?> getRoomByCode(@PathVariable String code) {
        return roomService.getRoomByCode(code)
                .map(room -> new ResponseEntity<>(room, HttpStatus.OK))
                .orElse(new ResponseEntity<>(new MessageResponse("Room not found with code: " + code), HttpStatus.NOT_FOUND));
    }
    
    @GetMapping("/campus/{campusId}")
    public ResponseEntity<List<Room>> getRoomsByCampus(@PathVariable Long campusId) {
        List<Room> rooms = roomService.getRoomsByCampus(campusId);
        return new ResponseEntity<>(rooms, HttpStatus.OK);
    }
    
    @GetMapping("/campus/{campusId}/type/{roomType}")
    public ResponseEntity<List<Room>> getRoomsByCampusAndType(
            @PathVariable Long campusId, 
            @PathVariable Room.RoomType roomType) {
        List<Room> rooms = roomService.getRoomsByCampusAndType(campusId, roomType);
        return new ResponseEntity<>(rooms, HttpStatus.OK);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createRoom(@Valid @RequestBody Room room) {
        if (roomService.existsByCode(room.getCode())) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: Room code is already taken!"));
        }
        
        Room createdRoom = roomService.createRoom(room);
        return new ResponseEntity<>(createdRoom, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateRoom(@PathVariable Long id, @Valid @RequestBody Room room) {
        try {
            Room updatedRoom = roomService.updateRoom(id, room);
            return new ResponseEntity<>(updatedRoom, HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteRoom(@PathVariable Long id) {
        try {
            roomService.deleteRoom(id);
            return new ResponseEntity<>(new MessageResponse("Room deleted successfully"), HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }
}
