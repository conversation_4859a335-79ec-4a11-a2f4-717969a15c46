package com.smartscheduler.controller;

import com.smartscheduler.dto.MessageResponse;
import com.smartscheduler.model.Class;
import com.smartscheduler.service.ClassService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/classes")
public class ClassController {

    @Autowired
    private ClassService classService;

    @GetMapping
    public ResponseEntity<List<Class>> getAllClasses() {
        List<Class> classes = classService.getAllClasses();
        return ResponseEntity.ok(classes);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getClassById(@PathVariable Long id) {
        return classService.getClassById(id)
                .<ResponseEntity<?>>map(classEntity -> ResponseEntity.ok(classEntity))
                .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new MessageResponse("Class not found with id: " + id)));
    }

    @GetMapping("/code/{code}")
    public ResponseEntity<?> getClassByCode(@PathVariable String code) {
        return classService.getClassByCode(code)
                .<ResponseEntity<?>>map(classEntity -> ResponseEntity.ok(classEntity))
                .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new MessageResponse("Class not found with code: " + code)));
    }

    @GetMapping("/major/{majorId}")
    public ResponseEntity<List<Class>> getClassesByMajor(@PathVariable Long majorId) {
        List<Class> classes = classService.getClassesByMajor(majorId);
        return ResponseEntity.ok(classes);
    }

    @GetMapping("/major/{majorId}/year/{year}")
    public ResponseEntity<List<Class>> getClassesByMajorAndYear(
            @PathVariable Long majorId,
            @PathVariable Integer year) {
        List<Class> classes = classService.getClassesByMajorAndYear(majorId, year);
        return ResponseEntity.ok(classes);
    }

    @GetMapping("/major/{majorId}/year/{year}/semester/{semester}")
    public ResponseEntity<List<Class>> getClassesByMajorAndYearAndSemester(
            @PathVariable Long majorId,
            @PathVariable Integer year,
            @PathVariable Integer semester) {
        List<Class> classes = classService.getClassesByMajorAndYearAndSemester(majorId, year, semester);
        return ResponseEntity.ok(classes);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createClass(@Valid @RequestBody Class classEntity) {
        if (classService.existsByCode(classEntity.getCode())) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: Class code is already taken!"));
        }

        Class createdClass = classService.createClass(classEntity);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdClass);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateClass(@PathVariable Long id, @Valid @RequestBody Class classEntity) {
        try {
            Class updatedClass = classService.updateClass(id, classEntity);
            return ResponseEntity.ok(updatedClass);
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(new MessageResponse(e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteClass(@PathVariable Long id) {
        try {
            classService.deleteClass(id);
            return ResponseEntity.ok(new MessageResponse("Class deleted successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(new MessageResponse(e.getMessage()));
        }
    }
}
