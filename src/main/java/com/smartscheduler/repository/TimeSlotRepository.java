package com.smartscheduler.repository;

import com.smartscheduler.model.TimeSlot;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TimeSlotRepository extends JpaRepository<TimeSlot, Long> {
    List<TimeSlot> findBySession(TimeSlot.Session session);
    List<TimeSlot> findByOrderByStartTimeAsc();
}
