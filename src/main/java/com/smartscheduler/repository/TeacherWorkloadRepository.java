package com.smartscheduler.repository;

import com.smartscheduler.model.Teacher;
import com.smartscheduler.model.TeacherWorkload;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TeacherWorkloadRepository extends JpaRepository<TeacherWorkload, Long> {
    List<TeacherWorkload> findByTeacher(Teacher teacher);
    List<TeacherWorkload> findByTeacherAndAcademicYear(Teacher teacher, String academicYear);
    List<TeacherWorkload> findByTeacherAndAcademicYearAndSemester(Teacher teacher, String academicYear, Integer semester);
    
    @Query("SELECT tw FROM TeacherWorkload tw WHERE tw.teacher = ?1 AND tw.teachingDate BETWEEN ?2 AND ?3")
    List<TeacherWorkload> findByTeacherAndDateRange(Teacher teacher, LocalDate startDate, LocalDate endDate);
    
    @Query("SELECT SUM(tw.weightedHours) FROM TeacherWorkload tw WHERE tw.teacher = ?1 AND tw.academicYear = ?2")
    Double sumWeightedHoursByTeacherAndAcademicYear(Teacher teacher, String academicYear);
    
    @Query("SELECT SUM(tw.weightedHours) FROM TeacherWorkload tw WHERE tw.teacher = ?1 AND tw.teachingDate BETWEEN ?2 AND ?3")
    Double sumWeightedHoursByTeacherAndDateRange(Teacher teacher, LocalDate startDate, LocalDate endDate);
}
