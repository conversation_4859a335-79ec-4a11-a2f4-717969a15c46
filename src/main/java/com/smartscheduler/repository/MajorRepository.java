package com.smartscheduler.repository;

import com.smartscheduler.model.Department;
import com.smartscheduler.model.Major;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MajorRepository extends JpaRepository<Major, Long> {
    Optional<Major> findByCode(String code);
    List<Major> findByDepartment(Department department);
    boolean existsByCode(String code);
}
