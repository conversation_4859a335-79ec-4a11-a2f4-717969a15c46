package com.smartscheduler.repository;

import com.smartscheduler.model.Course;
import com.smartscheduler.model.Lesson;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LessonRepository extends JpaRepository<Lesson, Long> {
    List<Lesson> findByCourse(Course course);
    List<Lesson> findByCourseOrderBySequenceNumber(Course course);
}
