package com.smartscheduler.repository;

import com.smartscheduler.model.Campus;
import com.smartscheduler.model.Room;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RoomRepository extends JpaRepository<Room, Long> {
    Optional<Room> findByCode(String code);
    List<Room> findByCampus(Campus campus);
    List<Room> findByCampusAndRoomType(Campus campus, Room.RoomType roomType);
    boolean existsByCode(String code);
}
