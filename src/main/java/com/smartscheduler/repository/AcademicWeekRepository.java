package com.smartscheduler.repository;

import com.smartscheduler.model.AcademicWeek;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface AcademicWeekRepository extends JpaRepository<AcademicWeek, Long> {
    Optional<AcademicWeek> findByWeekNumberAndAcademicYearAndSemester(Integer weekNumber, String academicYear, Integer semester);
    List<AcademicWeek> findByAcademicYearAndSemester(String academicYear, Integer semester);
    List<AcademicWeek> findByAcademicYearAndSemesterOrderByWeekNumberAsc(String academicYear, Integer semester);
    Optional<AcademicWeek> findByStartDateLessThanEqualAndEndDateGreaterThanEqual(LocalDate date, LocalDate sameDate);
}
