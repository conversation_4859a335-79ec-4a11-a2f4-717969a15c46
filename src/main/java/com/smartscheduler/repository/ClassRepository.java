package com.smartscheduler.repository;

import com.smartscheduler.model.Class;
import com.smartscheduler.model.Major;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ClassRepository extends JpaRepository<Class, Long> {
    Optional<Class> findByCode(String code);
    List<Class> findByMajor(Major major);
    List<Class> findByMajorAndYear(Major major, Integer year);
    List<Class> findByMajorAndYearAndSemester(Major major, Integer year, Integer semester);
    boolean existsByCode(String code);
}
