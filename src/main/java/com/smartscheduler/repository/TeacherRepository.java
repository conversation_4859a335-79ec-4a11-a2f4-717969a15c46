package com.smartscheduler.repository;

import com.smartscheduler.model.Department;
import com.smartscheduler.model.Teacher;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TeacherRepository extends JpaRepository<Teacher, Long> {
    Optional<Teacher> findByTeacherCode(String teacherCode);
    Optional<Teacher> findByEmail(String email);
    List<Teacher> findByDepartment(Department department);
    boolean existsByTeacherCode(String teacherCode);
    boolean existsByEmail(String email);
}
