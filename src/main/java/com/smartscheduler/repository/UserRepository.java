package com.smartscheduler.repository;

import com.smartscheduler.model.Teacher;
import com.smartscheduler.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
    Optional<User> findByEmail(String email);
    Optional<User> findByTeacher(Teacher teacher);
    boolean existsByUsername(String username);
    boolean existsByEmail(String email);
}
