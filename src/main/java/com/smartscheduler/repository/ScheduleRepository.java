package com.smartscheduler.repository;

import com.smartscheduler.model.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.lang.Class;
import java.util.List;

@Repository
public interface ScheduleRepository extends JpaRepository<Schedule, Long> {
    List<Schedule> findBy<PERSON>eacher(Teacher teacher);
    List<Schedule> findByTeacherAndAcademicWeek(Teacher teacher, AcademicWeek academicWeek);
    List<Schedule> findByClassEntity(com.smartscheduler.model.Class classEntity);
    List<Schedule> findByClassEntityAndAcademicWeek(com.smartscheduler.model.Class classEntity, AcademicWeek academicWeek);
    List<Schedule> findByRoom(Room room);
    List<Schedule> findByRoomAndAcademicWeek(Room room, AcademicWeek academicWeek);
    List<Schedule> findByAcademicWeek(AcademicWeek academicWeek);

    @Query("SELECT s FROM Schedule s WHERE s.academicWeek = ?1 AND s.weekDay = ?2 AND s.timeSlot = ?3 AND s.room = ?4")
    List<Schedule> findConflictingRoomSchedules(AcademicWeek academicWeek, WeekDay weekDay, TimeSlot timeSlot, Room room);

    @Query("SELECT s FROM Schedule s WHERE s.academicWeek = ?1 AND s.weekDay = ?2 AND s.timeSlot = ?3 AND s.teacher = ?4")
    List<Schedule> findConflictingTeacherSchedules(AcademicWeek academicWeek, WeekDay weekDay, TimeSlot timeSlot, Teacher teacher);

    @Query("SELECT s FROM Schedule s WHERE s.academicWeek = ?1 AND s.weekDay = ?2 AND s.timeSlot = ?3 AND s.classEntity = ?4")
    List<Schedule> findConflictingClassSchedules(AcademicWeek academicWeek, WeekDay weekDay, TimeSlot timeSlot, Class classEntity);
}
