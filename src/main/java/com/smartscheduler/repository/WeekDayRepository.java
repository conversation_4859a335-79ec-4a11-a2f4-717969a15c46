package com.smartscheduler.repository;

import com.smartscheduler.model.WeekDay;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface WeekDayRepository extends JpaRepository<WeekDay, Long> {
    Optional<WeekDay> findByName(String name);
    Optional<WeekDay> findByDayOrder(Integer dayOrder);
    List<WeekDay> findByOrderByDayOrderAsc();
}
