package com.smartscheduler.service;

import com.smartscheduler.model.*;
import com.smartscheduler.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class ScheduleService {

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private TeacherRepository teacherRepository;

    @Autowired
    private CourseRepository courseRepository;

    @Autowired
    private ClassRepository classRepository;

    @Autowired
    private RoomRepository roomRepository;

    @Autowired
    private TimeSlotRepository timeSlotRepository;

    @Autowired
    private WeekDayRepository weekDayRepository;

    @Autowired
    private AcademicWeekRepository academicWeekRepository;

    @Autowired
    private LessonRepository lessonRepository;

    @Autowired
    private TeacherWorkloadService teacherWorkloadService;

    public List<Schedule> getAllSchedules() {
        return scheduleRepository.findAll();
    }

    public Optional<Schedule> getScheduleById(Long id) {
        return scheduleRepository.findById(id);
    }

    public List<Schedule> getSchedulesByTeacher(Long teacherId) {
        Teacher teacher = teacherRepository.findById(teacherId)
                .orElseThrow(() -> new RuntimeException("Teacher not found with id: " + teacherId));

        return scheduleRepository.findByTeacher(teacher);
    }

    public List<Schedule> getSchedulesByTeacherAndWeek(Long teacherId, Long weekId) {
        Teacher teacher = teacherRepository.findById(teacherId)
                .orElseThrow(() -> new RuntimeException("Teacher not found with id: " + teacherId));

        AcademicWeek academicWeek = academicWeekRepository.findById(weekId)
                .orElseThrow(() -> new RuntimeException("Academic week not found with id: " + weekId));

        return scheduleRepository.findByTeacherAndAcademicWeek(teacher, academicWeek);
    }

    public List<Schedule> getSchedulesByClass(Long classId) {
        com.smartscheduler.model.Class classEntity = classRepository.findById(classId)
                .orElseThrow(() -> new RuntimeException("Class not found with id: " + classId));

        return scheduleRepository.findByClassEntity(classEntity);
    }

    public List<Schedule> getSchedulesByClassAndWeek(Long classId, Long weekId) {
        com.smartscheduler.model.Class classEntity = classRepository.findById(classId)
                .orElseThrow(() -> new RuntimeException("Class not found with id: " + classId));

        AcademicWeek academicWeek = academicWeekRepository.findById(weekId)
                .orElseThrow(() -> new RuntimeException("Academic week not found with id: " + weekId));

        return scheduleRepository.findByClassEntityAndAcademicWeek(classEntity, academicWeek);
    }

    public List<Schedule> getSchedulesByRoom(Long roomId) {
        Room room = roomRepository.findById(roomId)
                .orElseThrow(() -> new RuntimeException("Room not found with id: " + roomId));

        return scheduleRepository.findByRoom(room);
    }

    public List<Schedule> getSchedulesByRoomAndWeek(Long roomId, Long weekId) {
        Room room = roomRepository.findById(roomId)
                .orElseThrow(() -> new RuntimeException("Room not found with id: " + roomId));

        AcademicWeek academicWeek = academicWeekRepository.findById(weekId)
                .orElseThrow(() -> new RuntimeException("Academic week not found with id: " + weekId));

        return scheduleRepository.findByRoomAndAcademicWeek(room, academicWeek);
    }

    public List<Schedule> getSchedulesByWeek(Long weekId) {
        AcademicWeek academicWeek = academicWeekRepository.findById(weekId)
                .orElseThrow(() -> new RuntimeException("Academic week not found with id: " + weekId));

        return scheduleRepository.findByAcademicWeek(academicWeek);
    }

    @Transactional
    public Schedule createSchedule(Schedule schedule) {
        validateScheduleEntities(schedule);
        checkForScheduleConflicts(schedule);

        Schedule savedSchedule = scheduleRepository.save(schedule);

        // Create teacher workload record
        teacherWorkloadService.createWorkloadFromSchedule(savedSchedule);

        return savedSchedule;
    }

    @Transactional
    public Schedule updateSchedule(Long id, Schedule scheduleDetails) {
        Schedule schedule = scheduleRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Schedule not found with id: " + id));

        // Update schedule fields
        schedule.setTeachingFormat(scheduleDetails.getTeachingFormat());
        schedule.setPracticeGroup(scheduleDetails.getPracticeGroup());
        schedule.setHours(scheduleDetails.getHours());
        schedule.setCoefficient(scheduleDetails.getCoefficient());
        schedule.setNotes(scheduleDetails.getNotes());

        // Update relationships if provided
        updateScheduleRelationships(schedule, scheduleDetails);

        // Check for conflicts with other schedules
        checkForScheduleConflicts(schedule);

        Schedule updatedSchedule = scheduleRepository.save(schedule);

        // Update teacher workload record
        teacherWorkloadService.updateWorkloadFromSchedule(updatedSchedule);

        return updatedSchedule;
    }

    @Transactional
    public void deleteSchedule(Long id) {
        Schedule schedule = scheduleRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Schedule not found with id: " + id));

        // Delete associated teacher workload
        teacherWorkloadService.deleteWorkloadForSchedule(schedule);

        scheduleRepository.delete(schedule);
    }

    private void validateScheduleEntities(Schedule schedule) {
        // Validate teacher
        if (schedule.getTeacher() == null || schedule.getTeacher().getId() == null) {
            throw new RuntimeException("Teacher is required");
        }
        teacherRepository.findById(schedule.getTeacher().getId())
                .orElseThrow(() -> new RuntimeException("Teacher not found with id: " + schedule.getTeacher().getId()));

        // Validate course
        if (schedule.getCourse() == null || schedule.getCourse().getId() == null) {
            throw new RuntimeException("Course is required");
        }
        courseRepository.findById(schedule.getCourse().getId())
                .orElseThrow(() -> new RuntimeException("Course not found with id: " + schedule.getCourse().getId()));

        // Validate class
        if (schedule.getClassEntity() == null || schedule.getClassEntity().getId() == null) {
            throw new RuntimeException("Class is required");
        }
        classRepository.findById(schedule.getClassEntity().getId())
                .orElseThrow(() -> new RuntimeException("Class not found with id: " + schedule.getClassEntity().getId()));

        // Validate room
        if (schedule.getRoom() == null || schedule.getRoom().getId() == null) {
            throw new RuntimeException("Room is required");
        }
        roomRepository.findById(schedule.getRoom().getId())
                .orElseThrow(() -> new RuntimeException("Room not found with id: " + schedule.getRoom().getId()));

        // Validate time slot
        if (schedule.getTimeSlot() == null || schedule.getTimeSlot().getId() == null) {
            throw new RuntimeException("Time slot is required");
        }
        timeSlotRepository.findById(schedule.getTimeSlot().getId())
                .orElseThrow(() -> new RuntimeException("Time slot not found with id: " + schedule.getTimeSlot().getId()));

        // Validate week day
        if (schedule.getWeekDay() == null || schedule.getWeekDay().getId() == null) {
            throw new RuntimeException("Week day is required");
        }
        weekDayRepository.findById(schedule.getWeekDay().getId())
                .orElseThrow(() -> new RuntimeException("Week day not found with id: " + schedule.getWeekDay().getId()));

        // Validate academic week
        if (schedule.getAcademicWeek() == null || schedule.getAcademicWeek().getId() == null) {
            throw new RuntimeException("Academic week is required");
        }
        academicWeekRepository.findById(schedule.getAcademicWeek().getId())
                .orElseThrow(() -> new RuntimeException("Academic week not found with id: " + schedule.getAcademicWeek().getId()));

        // Validate lesson if provided
        if (schedule.getLesson() != null && schedule.getLesson().getId() != null) {
            lessonRepository.findById(schedule.getLesson().getId())
                    .orElseThrow(() -> new RuntimeException("Lesson not found with id: " + schedule.getLesson().getId()));
        }
    }

    private void updateScheduleRelationships(Schedule schedule, Schedule scheduleDetails) {
        // Update teacher if provided
        if (scheduleDetails.getTeacher() != null && scheduleDetails.getTeacher().getId() != null) {
            Teacher teacher = teacherRepository.findById(scheduleDetails.getTeacher().getId())
                    .orElseThrow(() -> new RuntimeException("Teacher not found with id: " + scheduleDetails.getTeacher().getId()));
            schedule.setTeacher(teacher);
        }

        // Update course if provided
        if (scheduleDetails.getCourse() != null && scheduleDetails.getCourse().getId() != null) {
            Course course = courseRepository.findById(scheduleDetails.getCourse().getId())
                    .orElseThrow(() -> new RuntimeException("Course not found with id: " + scheduleDetails.getCourse().getId()));
            schedule.setCourse(course);
        }

        // Update lesson if provided
        if (scheduleDetails.getLesson() != null && scheduleDetails.getLesson().getId() != null) {
            Lesson lesson = lessonRepository.findById(scheduleDetails.getLesson().getId())
                    .orElseThrow(() -> new RuntimeException("Lesson not found with id: " + scheduleDetails.getLesson().getId()));
            schedule.setLesson(lesson);
        } else {
            schedule.setLesson(null);
        }

        // Update class if provided
        if (scheduleDetails.getClassEntity() != null && scheduleDetails.getClassEntity().getId() != null) {
            com.smartscheduler.model.Class classEntity = classRepository.findById(scheduleDetails.getClassEntity().getId())
                    .orElseThrow(() -> new RuntimeException("Class not found with id: " + scheduleDetails.getClassEntity().getId()));
            schedule.setClassEntity(classEntity);
        }

        // Update room if provided
        if (scheduleDetails.getRoom() != null && scheduleDetails.getRoom().getId() != null) {
            Room room = roomRepository.findById(scheduleDetails.getRoom().getId())
                    .orElseThrow(() -> new RuntimeException("Room not found with id: " + scheduleDetails.getRoom().getId()));
            schedule.setRoom(room);
        }

        // Update time slot if provided
        if (scheduleDetails.getTimeSlot() != null && scheduleDetails.getTimeSlot().getId() != null) {
            TimeSlot timeSlot = timeSlotRepository.findById(scheduleDetails.getTimeSlot().getId())
                    .orElseThrow(() -> new RuntimeException("Time slot not found with id: " + scheduleDetails.getTimeSlot().getId()));
            schedule.setTimeSlot(timeSlot);
        }

        // Update week day if provided
        if (scheduleDetails.getWeekDay() != null && scheduleDetails.getWeekDay().getId() != null) {
            WeekDay weekDay = weekDayRepository.findById(scheduleDetails.getWeekDay().getId())
                    .orElseThrow(() -> new RuntimeException("Week day not found with id: " + scheduleDetails.getWeekDay().getId()));
            schedule.setWeekDay(weekDay);
        }

        // Update academic week if provided
        if (scheduleDetails.getAcademicWeek() != null && scheduleDetails.getAcademicWeek().getId() != null) {
            AcademicWeek academicWeek = academicWeekRepository.findById(scheduleDetails.getAcademicWeek().getId())
                    .orElseThrow(() -> new RuntimeException("Academic week not found with id: " + scheduleDetails.getAcademicWeek().getId()));
            schedule.setAcademicWeek(academicWeek);
        }
    }

    private void checkForScheduleConflicts(Schedule schedule) {
        // Check for room conflicts
        List<Schedule> roomConflicts = scheduleRepository.findConflictingRoomSchedules(
                schedule.getAcademicWeek(),
                schedule.getWeekDay(),
                schedule.getTimeSlot(),
                schedule.getRoom());

        if (!roomConflicts.isEmpty() && (schedule.getId() == null || roomConflicts.stream().anyMatch(s -> !s.getId().equals(schedule.getId())))) {
            throw new RuntimeException("Room is already scheduled for this time slot");
        }

        // Check for teacher conflicts
        List<Schedule> teacherConflicts = scheduleRepository.findConflictingTeacherSchedules(
                schedule.getAcademicWeek(),
                schedule.getWeekDay(),
                schedule.getTimeSlot(),
                schedule.getTeacher());

        if (!teacherConflicts.isEmpty() && (schedule.getId() == null || teacherConflicts.stream().anyMatch(s -> !s.getId().equals(schedule.getId())))) {
            throw new RuntimeException("Teacher is already scheduled for this time slot");
        }

        // Check for class conflicts
        List<Schedule> classConflicts = scheduleRepository.findConflictingClassSchedules(
                schedule.getAcademicWeek(),
                schedule.getWeekDay(),
                schedule.getTimeSlot(),
                schedule.getClassEntity());

        if (!classConflicts.isEmpty() && (schedule.getId() == null || classConflicts.stream().anyMatch(s -> !s.getId().equals(schedule.getId())))) {
            throw new RuntimeException("Class is already scheduled for this time slot");
        }
    }
}
