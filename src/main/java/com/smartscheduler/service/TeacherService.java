package com.smartscheduler.service;

import com.smartscheduler.model.Department;
import com.smartscheduler.model.Teacher;
import com.smartscheduler.repository.DepartmentRepository;
import com.smartscheduler.repository.TeacherRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class TeacherService {

    @Autowired
    private TeacherRepository teacherRepository;
    
    @Autowired
    private DepartmentRepository departmentRepository;

    public List<Teacher> getAllTeachers() {
        return teacherRepository.findAll();
    }

    public Optional<Teacher> getTeacherById(Long id) {
        return teacherRepository.findById(id);
    }

    public Optional<Teacher> getTeacherByCode(String code) {
        return teacherRepository.findByTeacherCode(code);
    }
    
    public List<Teacher> getTeachersByDepartment(Long departmentId) {
        Department department = departmentRepository.findById(departmentId)
                .orElseThrow(() -> new RuntimeException("Department not found with id: " + departmentId));
        
        return teacherRepository.findByDepartment(department);
    }

    @Transactional
    public Teacher createTeacher(Teacher teacher) {
        if (teacher.getDepartment() != null && teacher.getDepartment().getId() != null) {
            Department department = departmentRepository.findById(teacher.getDepartment().getId())
                    .orElseThrow(() -> new RuntimeException("Department not found with id: " + teacher.getDepartment().getId()));
            
            teacher.setDepartment(department);
        }
        
        return teacherRepository.save(teacher);
    }

    @Transactional
    public Teacher updateTeacher(Long id, Teacher teacherDetails) {
        Teacher teacher = teacherRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Teacher not found with id: " + id));
        
        teacher.setFirstName(teacherDetails.getFirstName());
        teacher.setLastName(teacherDetails.getLastName());
        teacher.setEmail(teacherDetails.getEmail());
        teacher.setPhone(teacherDetails.getPhone());
        teacher.setTeacherCode(teacherDetails.getTeacherCode());
        
        if (teacherDetails.getDepartment() != null && teacherDetails.getDepartment().getId() != null) {
            Department department = departmentRepository.findById(teacherDetails.getDepartment().getId())
                    .orElseThrow(() -> new RuntimeException("Department not found with id: " + teacherDetails.getDepartment().getId()));
            
            teacher.setDepartment(department);
        }
        
        return teacherRepository.save(teacher);
    }

    @Transactional
    public void deleteTeacher(Long id) {
        Teacher teacher = teacherRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Teacher not found with id: " + id));
        
        teacherRepository.delete(teacher);
    }

    public boolean existsByTeacherCode(String code) {
        return teacherRepository.existsByTeacherCode(code);
    }
    
    public boolean existsByEmail(String email) {
        return teacherRepository.existsByEmail(email);
    }
}
