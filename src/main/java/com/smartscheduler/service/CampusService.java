package com.smartscheduler.service;

import com.smartscheduler.model.Campus;
import com.smartscheduler.repository.CampusRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class CampusService {

    @Autowired
    private CampusRepository campusRepository;

    public List<Campus> getAllCampuses() {
        return campusRepository.findAll();
    }

    public Optional<Campus> getCampusById(Long id) {
        return campusRepository.findById(id);
    }

    public Optional<Campus> getCampusByCode(String code) {
        return campusRepository.findByCode(code);
    }

    @Transactional
    public Campus createCampus(Campus campus) {
        return campusRepository.save(campus);
    }

    @Transactional
    public Campus updateCampus(Long id, Campus campusDetails) {
        Campus campus = campusRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Campus not found with id: " + id));
        
        campus.setName(campusDetails.getName());
        campus.setCode(campusDetails.getCode());
        campus.setAddress(campusDetails.getAddress());
        
        return campusRepository.save(campus);
    }

    @Transactional
    public void deleteCampus(Long id) {
        Campus campus = campusRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Campus not found with id: " + id));
        
        campusRepository.delete(campus);
    }

    public boolean existsByCode(String code) {
        return campusRepository.existsByCode(code);
    }
}
