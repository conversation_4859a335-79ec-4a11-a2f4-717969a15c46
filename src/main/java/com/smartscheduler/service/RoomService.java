package com.smartscheduler.service;

import com.smartscheduler.model.Campus;
import com.smartscheduler.model.Room;
import com.smartscheduler.repository.CampusRepository;
import com.smartscheduler.repository.RoomRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class RoomService {

    @Autowired
    private RoomRepository roomRepository;
    
    @Autowired
    private CampusRepository campusRepository;

    public List<Room> getAllRooms() {
        return roomRepository.findAll();
    }

    public Optional<Room> getRoomById(Long id) {
        return roomRepository.findById(id);
    }

    public Optional<Room> getRoomByCode(String code) {
        return roomRepository.findByCode(code);
    }
    
    public List<Room> getRoomsByCampus(Long campusId) {
        Campus campus = campusRepository.findById(campusId)
                .orElseThrow(() -> new RuntimeException("Campus not found with id: " + campusId));
        
        return roomRepository.findByCampus(campus);
    }
    
    public List<Room> getRoomsByCampusAndType(Long campusId, Room.RoomType roomType) {
        Campus campus = campusRepository.findById(campusId)
                .orElseThrow(() -> new RuntimeException("Campus not found with id: " + campusId));
        
        return roomRepository.findByCampusAndRoomType(campus, roomType);
    }

    @Transactional
    public Room createRoom(Room room) {
        if (room.getCampus() != null && room.getCampus().getId() != null) {
            Campus campus = campusRepository.findById(room.getCampus().getId())
                    .orElseThrow(() -> new RuntimeException("Campus not found with id: " + room.getCampus().getId()));
            
            room.setCampus(campus);
        }
        
        return roomRepository.save(room);
    }

    @Transactional
    public Room updateRoom(Long id, Room roomDetails) {
        Room room = roomRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Room not found with id: " + id));
        
        room.setName(roomDetails.getName());
        room.setCode(roomDetails.getCode());
        room.setCapacity(roomDetails.getCapacity());
        room.setRoomType(roomDetails.getRoomType());
        
        if (roomDetails.getCampus() != null && roomDetails.getCampus().getId() != null) {
            Campus campus = campusRepository.findById(roomDetails.getCampus().getId())
                    .orElseThrow(() -> new RuntimeException("Campus not found with id: " + roomDetails.getCampus().getId()));
            
            room.setCampus(campus);
        }
        
        return roomRepository.save(room);
    }

    @Transactional
    public void deleteRoom(Long id) {
        Room room = roomRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Room not found with id: " + id));
        
        roomRepository.delete(room);
    }

    public boolean existsByCode(String code) {
        return roomRepository.existsByCode(code);
    }
}
