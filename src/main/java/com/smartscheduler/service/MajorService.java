package com.smartscheduler.service;

import com.smartscheduler.model.Department;
import com.smartscheduler.model.Major;
import com.smartscheduler.repository.DepartmentRepository;
import com.smartscheduler.repository.MajorRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class MajorService {

    @Autowired
    private MajorRepository majorRepository;
    
    @Autowired
    private DepartmentRepository departmentRepository;

    public List<Major> getAllMajors() {
        return majorRepository.findAll();
    }

    public Optional<Major> getMajorById(Long id) {
        return majorRepository.findById(id);
    }

    public Optional<Major> getMajorByCode(String code) {
        return majorRepository.findByCode(code);
    }
    
    public List<Major> getMajorsByDepartment(Long departmentId) {
        Department department = departmentRepository.findById(departmentId)
                .orElseThrow(() -> new RuntimeException("Department not found with id: " + departmentId));
        
        return majorRepository.findByDepartment(department);
    }

    @Transactional
    public Major createMajor(Major major) {
        if (major.getDepartment() != null && major.getDepartment().getId() != null) {
            Department department = departmentRepository.findById(major.getDepartment().getId())
                    .orElseThrow(() -> new RuntimeException("Department not found with id: " + major.getDepartment().getId()));
            
            major.setDepartment(department);
        }
        
        return majorRepository.save(major);
    }

    @Transactional
    public Major updateMajor(Long id, Major majorDetails) {
        Major major = majorRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Major not found with id: " + id));
        
        major.setName(majorDetails.getName());
        major.setCode(majorDetails.getCode());
        major.setDescription(majorDetails.getDescription());
        
        if (majorDetails.getDepartment() != null && majorDetails.getDepartment().getId() != null) {
            Department department = departmentRepository.findById(majorDetails.getDepartment().getId())
                    .orElseThrow(() -> new RuntimeException("Department not found with id: " + majorDetails.getDepartment().getId()));
            
            major.setDepartment(department);
        }
        
        return majorRepository.save(major);
    }

    @Transactional
    public void deleteMajor(Long id) {
        Major major = majorRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Major not found with id: " + id));
        
        majorRepository.delete(major);
    }

    public boolean existsByCode(String code) {
        return majorRepository.existsByCode(code);
    }
}
