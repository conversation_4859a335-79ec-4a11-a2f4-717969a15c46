package com.smartscheduler.service;

import com.smartscheduler.model.Class;
import com.smartscheduler.model.Major;
import com.smartscheduler.repository.ClassRepository;
import com.smartscheduler.repository.MajorRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class ClassService {

    @Autowired
    private ClassRepository classRepository;
    
    @Autowired
    private MajorRepository majorRepository;

    public List<Class> getAllClasses() {
        return classRepository.findAll();
    }

    public Optional<Class> getClassById(Long id) {
        return classRepository.findById(id);
    }

    public Optional<Class> getClassByCode(String code) {
        return classRepository.findByCode(code);
    }
    
    public List<Class> getClassesByMajor(Long majorId) {
        Major major = majorRepository.findById(majorId)
                .orElseThrow(() -> new RuntimeException("Major not found with id: " + majorId));
        
        return classRepository.findByMajor(major);
    }
    
    public List<Class> getClassesByMajorAndYear(Long majorId, Integer year) {
        Major major = majorRepository.findById(majorId)
                .orElseThrow(() -> new RuntimeException("Major not found with id: " + majorId));
        
        return classRepository.findByMajorAndYear(major, year);
    }
    
    public List<Class> getClassesByMajorAndYearAndSemester(Long majorId, Integer year, Integer semester) {
        Major major = majorRepository.findById(majorId)
                .orElseThrow(() -> new RuntimeException("Major not found with id: " + majorId));
        
        return classRepository.findByMajorAndYearAndSemester(major, year, semester);
    }

    @Transactional
    public Class createClass(Class classEntity) {
        if (classEntity.getMajor() != null && classEntity.getMajor().getId() != null) {
            Major major = majorRepository.findById(classEntity.getMajor().getId())
                    .orElseThrow(() -> new RuntimeException("Major not found with id: " + classEntity.getMajor().getId()));
            
            classEntity.setMajor(major);
        }
        
        return classRepository.save(classEntity);
    }

    @Transactional
    public Class updateClass(Long id, Class classDetails) {
        Class classEntity = classRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Class not found with id: " + id));
        
        classEntity.setName(classDetails.getName());
        classEntity.setCode(classDetails.getCode());
        classEntity.setYear(classDetails.getYear());
        classEntity.setSemester(classDetails.getSemester());
        classEntity.setStudentCount(classDetails.getStudentCount());
        
        if (classDetails.getMajor() != null && classDetails.getMajor().getId() != null) {
            Major major = majorRepository.findById(classDetails.getMajor().getId())
                    .orElseThrow(() -> new RuntimeException("Major not found with id: " + classDetails.getMajor().getId()));
            
            classEntity.setMajor(major);
        }
        
        return classRepository.save(classEntity);
    }

    @Transactional
    public void deleteClass(Long id) {
        Class classEntity = classRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Class not found with id: " + id));
        
        classRepository.delete(classEntity);
    }

    public boolean existsByCode(String code) {
        return classRepository.existsByCode(code);
    }
}
