package com.smartscheduler.service;

import com.smartscheduler.model.Schedule;
import com.smartscheduler.model.Teacher;
import com.smartscheduler.model.TeacherWorkload;
import com.smartscheduler.repository.TeacherRepository;
import com.smartscheduler.repository.TeacherWorkloadRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
public class TeacherWorkloadService {

    @Autowired
    private TeacherWorkloadRepository teacherWorkloadRepository;
    
    @Autowired
    private TeacherRepository teacherRepository;

    public List<TeacherWorkload> getAllWorkloads() {
        return teacherWorkloadRepository.findAll();
    }

    public Optional<TeacherWorkload> getWorkloadById(Long id) {
        return teacherWorkloadRepository.findById(id);
    }
    
    public List<TeacherWorkload> getWorkloadsByTeacher(Long teacherId) {
        Teacher teacher = teacherRepository.findById(teacherId)
                .orElseThrow(() -> new RuntimeException("Teacher not found with id: " + teacherId));
        
        return teacherWorkloadRepository.findByTeacher(teacher);
    }
    
    public List<TeacherWorkload> getWorkloadsByTeacherAndAcademicYear(Long teacherId, String academicYear) {
        Teacher teacher = teacherRepository.findById(teacherId)
                .orElseThrow(() -> new RuntimeException("Teacher not found with id: " + teacherId));
        
        return teacherWorkloadRepository.findByTeacherAndAcademicYear(teacher, academicYear);
    }
    
    public List<TeacherWorkload> getWorkloadsByTeacherAndDateRange(Long teacherId, LocalDate startDate, LocalDate endDate) {
        Teacher teacher = teacherRepository.findById(teacherId)
                .orElseThrow(() -> new RuntimeException("Teacher not found with id: " + teacherId));
        
        return teacherWorkloadRepository.findByTeacherAndDateRange(teacher, startDate, endDate);
    }
    
    public Double getTotalWeightedHoursByTeacherAndAcademicYear(Long teacherId, String academicYear) {
        Teacher teacher = teacherRepository.findById(teacherId)
                .orElseThrow(() -> new RuntimeException("Teacher not found with id: " + teacherId));
        
        Double total = teacherWorkloadRepository.sumWeightedHoursByTeacherAndAcademicYear(teacher, academicYear);
        return total != null ? total : 0.0;
    }
    
    public Double getTotalWeightedHoursByTeacherAndDateRange(Long teacherId, LocalDate startDate, LocalDate endDate) {
        Teacher teacher = teacherRepository.findById(teacherId)
                .orElseThrow(() -> new RuntimeException("Teacher not found with id: " + teacherId));
        
        Double total = teacherWorkloadRepository.sumWeightedHoursByTeacherAndDateRange(teacher, startDate, endDate);
        return total != null ? total : 0.0;
    }

    @Transactional
    public TeacherWorkload createWorkload(TeacherWorkload workload) {
        // Calculate weighted hours
        workload.calculateWeightedHours();
        
        return teacherWorkloadRepository.save(workload);
    }
    
    @Transactional
    public TeacherWorkload createWorkloadFromSchedule(Schedule schedule) {
        // Create a new workload record from schedule
        TeacherWorkload workload = TeacherWorkload.builder()
                .teacher(schedule.getTeacher())
                .course(schedule.getCourse())
                .classEntity(schedule.getClassEntity())
                .academicYear(schedule.getAcademicWeek().getAcademicYear())
                .semester(schedule.getAcademicWeek().getSemester())
                .teachingDate(schedule.getAcademicWeek().getStartDate().plusDays(schedule.getWeekDay().getDayOrder() - 1))
                .hours(schedule.getHours())
                .coefficient(schedule.getCoefficient())
                .teachingFormat(schedule.getTeachingFormat())
                .build();
        
        // Calculate weighted hours
        workload.calculateWeightedHours();
        
        return teacherWorkloadRepository.save(workload);
    }

    @Transactional
    public TeacherWorkload updateWorkload(Long id, TeacherWorkload workloadDetails) {
        TeacherWorkload workload = teacherWorkloadRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Workload not found with id: " + id));
        
        workload.setHours(workloadDetails.getHours());
        workload.setCoefficient(workloadDetails.getCoefficient());
        workload.setTeachingDate(workloadDetails.getTeachingDate());
        workload.setTeachingFormat(workloadDetails.getTeachingFormat());
        
        // Calculate weighted hours
        workload.calculateWeightedHours();
        
        return teacherWorkloadRepository.save(workload);
    }
    
    @Transactional
    public void updateWorkloadFromSchedule(Schedule schedule) {
        // Find existing workloads for this schedule's date and teacher
        LocalDate scheduleDate = schedule.getAcademicWeek().getStartDate().plusDays(schedule.getWeekDay().getDayOrder() - 1);
        List<TeacherWorkload> existingWorkloads = teacherWorkloadRepository.findByTeacherAndDateRange(
                schedule.getTeacher(), scheduleDate, scheduleDate);
        
        // Update existing workload or create new one
        if (!existingWorkloads.isEmpty()) {
            TeacherWorkload workload = existingWorkloads.get(0);
            workload.setCourse(schedule.getCourse());
            workload.setClassEntity(schedule.getClassEntity());
            workload.setAcademicYear(schedule.getAcademicWeek().getAcademicYear());
            workload.setSemester(schedule.getAcademicWeek().getSemester());
            workload.setHours(schedule.getHours());
            workload.setCoefficient(schedule.getCoefficient());
            workload.setTeachingFormat(schedule.getTeachingFormat());
            
            // Calculate weighted hours
            workload.calculateWeightedHours();
            
            teacherWorkloadRepository.save(workload);
        } else {
            createWorkloadFromSchedule(schedule);
        }
    }

    @Transactional
    public void deleteWorkload(Long id) {
        TeacherWorkload workload = teacherWorkloadRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Workload not found with id: " + id));
        
        teacherWorkloadRepository.delete(workload);
    }
    
    @Transactional
    public void deleteWorkloadForSchedule(Schedule schedule) {
        // Find workloads for this schedule's date and teacher
        LocalDate scheduleDate = schedule.getAcademicWeek().getStartDate().plusDays(schedule.getWeekDay().getDayOrder() - 1);
        List<TeacherWorkload> workloads = teacherWorkloadRepository.findByTeacherAndDateRange(
                schedule.getTeacher(), scheduleDate, scheduleDate);
        
        // Delete workloads
        workloads.forEach(teacherWorkloadRepository::delete);
    }
}
