// Node.js script to generate icons from SVG
// Run with: node generate-icons.js

const fs = require('fs');
const path = require('path');

// Simple favicon.ico generator (16x16 pixels)
function generateSimpleFavicon() {
    // This creates a very basic ICO file with a blue square and white "S"
    // ICO file format is complex, so this is a minimal implementation
    
    const icoHeader = Buffer.from([
        0x00, 0x00, // Reserved
        0x01, 0x00, // Type (1 = ICO)
        0x01, 0x00, // Number of images
    ]);
    
    const icoEntry = Buffer.from([
        0x10, // Width (16)
        0x10, // Height (16)
        0x00, // Color count (0 = no palette)
        0x00, // Reserved
        0x01, 0x00, // Color planes
        0x20, 0x00, // Bits per pixel (32)
        0x80, 0x02, 0x00, 0x00, // Image size (640 bytes)
        0x16, 0x00, 0x00, 0x00, // Image offset (22 bytes)
    ]);
    
    // Simple 16x16 RGBA bitmap data (blue background with white "S")
    const bitmapData = Buffer.alloc(640); // 16*16*4 bytes for RGBA + header
    
    // Fill with blue background (BGRA format for ICO)
    for (let i = 0; i < 16 * 16; i++) {
        const offset = i * 4;
        bitmapData[offset] = 0xd2;     // Blue
        bitmapData[offset + 1] = 0x76; // Green
        bitmapData[offset + 2] = 0x19; // Red
        bitmapData[offset + 3] = 0xff; // Alpha
    }
    
    // Add simple "S" pattern (very basic)
    const sPattern = [
        [4,5,6,7,8,9,10,11],
        [4],
        [4],
        [4,5,6,7],
        [11],
        [11],
        [4,5,6,7,8,9,10,11]
    ];
    
    sPattern.forEach((row, y) => {
        row.forEach(x => {
            if (y + 5 < 16 && x < 16) {
                const offset = ((y + 5) * 16 + x) * 4;
                bitmapData[offset] = 0xff;     // White
                bitmapData[offset + 1] = 0xff;
                bitmapData[offset + 2] = 0xff;
                bitmapData[offset + 3] = 0xff;
            }
        });
    });
    
    return Buffer.concat([icoHeader, icoEntry, bitmapData]);
}

// Generate favicon.ico
try {
    const faviconData = generateSimpleFavicon();
    fs.writeFileSync(path.join(__dirname, 'frontend/public/favicon.ico'), faviconData);
    console.log('✅ Generated favicon.ico');
} catch (error) {
    console.error('❌ Error generating favicon.ico:', error.message);
}

// Create simple PNG placeholders using Canvas (if available)
try {
    // Try to use canvas if available
    const { createCanvas } = require('canvas');
    
    function createPngLogo(size, filename) {
        const canvas = createCanvas(size, size);
        const ctx = canvas.getContext('2d');
        
        // Background
        const gradient = ctx.createLinearGradient(0, 0, size, size);
        gradient.addColorStop(0, '#1976d2');
        gradient.addColorStop(1, '#1565c0');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, size, size);
        
        // Simple calendar icon
        const calSize = size * 0.6;
        const calX = (size - calSize) / 2;
        const calY = (size - calSize) / 2;
        
        ctx.fillStyle = 'white';
        ctx.fillRect(calX, calY, calSize, calSize);
        
        // Save as PNG
        const buffer = canvas.toBuffer('image/png');
        fs.writeFileSync(path.join(__dirname, `frontend/public/${filename}`), buffer);
        console.log(`✅ Generated ${filename}`);
    }
    
    createPngLogo(192, 'logo192.png');
    createPngLogo(512, 'logo512.png');
    
} catch (error) {
    console.log('⚠️  Canvas not available. Please install with: npm install canvas');
    console.log('   Or manually create PNG files from the SVG files using an online converter.');
}

console.log('\n📋 Manual steps if needed:');
console.log('1. Open create-favicon.html in your browser');
console.log('2. Click "Generate Favicon" and download the result');
console.log('3. Convert SVG files to PNG using online tools like:');
console.log('   - https://convertio.co/svg-png/');
console.log('   - https://cloudconvert.com/svg-to-png');
console.log('4. Place the files in frontend/public/');

console.log('\n🔧 Quick fix for development:');
console.log('You can also temporarily comment out the favicon and manifest links in frontend/public/index.html');
