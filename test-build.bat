@echo off
echo Testing Smart Scheduler build...

echo Setting Java 17 environment...
set "JAVA_HOME=C:\Program Files\Java\jdk-17"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo.
echo Java version:
java -version

echo.
echo Maven version:
mvn -version

echo.
echo Running clean compile...
mvn clean compile -DskipTests

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful!
) else (
    echo.
    echo Build failed!
)

pause
