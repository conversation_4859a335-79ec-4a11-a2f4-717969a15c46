# Khắc phục lỗi StackOverflowError

## Vấn đề
```
Caused by: java.lang.StackOverflowError: null
	at java.base/java.math.BigDecimal.add(BigDecimal.java:4994)
```

## Nguyên nhân
Lỗi `StackOverflowError` xảy ra do **circular references** trong các quan hệ bidirectional của JPA entities khi Jackson cố gắng serialize objects thành JSON. Khi một entity có quan hệ với entity khác và ngược lại, Jackson sẽ cố gắng serialize vô hạn, dẫn đến stack overflow.

### Ví dụ về circular reference:
```
Department → Teacher → Department → Teacher → ... (vô hạn)
Major → Class → Major → Class → ... (vô hạn)
```

## Giải pháp đã thực hiện

### 1. Sử dụng Jackson Annotations

#### @JsonManagedReference và @JsonBackReference
- **@JsonManagedReference**: Đặt ở phía "parent" (forward part of reference)
- **@JsonBackReference**: Đặt ở phía "child" (back part of reference)

#### @JsonIgnore
- Bỏ qua hoàn toàn field khi serialize JSON
- Sử dụng cho các collections không cần thiết trong API response

### 2. Cập nhật các Entity Models

#### Department.java
```java
@JsonManagedReference("department-majors")
@OneToMany(mappedBy = "department", cascade = CascadeType.ALL)
private Set<Major> majors = new HashSet<>();

@JsonManagedReference("department-teachers")
@OneToMany(mappedBy = "department", cascade = CascadeType.ALL)
private Set<Teacher> teachers = new HashSet<>();
```

#### Major.java
```java
@JsonBackReference("department-majors")
@ManyToOne(fetch = FetchType.LAZY)
@JoinColumn(name = "department_id", nullable = false)
private Department department;

@JsonManagedReference("major-classes")
@OneToMany(mappedBy = "major", cascade = CascadeType.ALL)
private Set<Class> classes = new HashSet<>();
```

#### Class.java
```java
@JsonBackReference("major-classes")
@ManyToOne(fetch = FetchType.LAZY)
@JoinColumn(name = "major_id", nullable = false)
private Major major;

@JsonIgnore
@OneToMany(mappedBy = "classEntity", cascade = CascadeType.ALL)
private Set<Schedule> schedules = new HashSet<>();
```

#### Teacher.java
```java
@JsonBackReference("department-teachers")
@ManyToOne(fetch = FetchType.LAZY)
@JoinColumn(name = "department_id", nullable = false)
private Department department;

@JsonIgnore
@OneToOne(mappedBy = "teacher", cascade = CascadeType.ALL)
private User user;

@JsonIgnore
@OneToMany(mappedBy = "teacher", cascade = CascadeType.ALL)
private Set<Schedule> schedules = new HashSet<>();
```

#### Campus.java
```java
@JsonManagedReference("campus-rooms")
@OneToMany(mappedBy = "campus", cascade = CascadeType.ALL)
private Set<Room> rooms = new HashSet<>();
```

#### Room.java
```java
@JsonBackReference("campus-rooms")
@ManyToOne(fetch = FetchType.LAZY)
@JoinColumn(name = "campus_id", nullable = false)
private Campus campus;

@JsonIgnore
@OneToMany(mappedBy = "room", cascade = CascadeType.ALL)
private Set<Schedule> schedules = new HashSet<>();
```

#### User.java
```java
@JsonIgnore
@Column(name = "password", nullable = false)
private String password;

@JsonIgnore
@OneToOne(fetch = FetchType.LAZY)
@JoinColumn(name = "teacher_id")
private Teacher teacher;

@JsonIgnore
@ManyToMany(fetch = FetchType.EAGER)
private Set<Role> roles = new HashSet<>();
```

### 3. Entities với @JsonIgnore cho Collections

Các entities sau đã được cập nhật với `@JsonIgnore` cho các collections:

- **Course**: `lessons`, `schedules`
- **TimeSlot**: `schedules`
- **WeekDay**: `schedules`
- **AcademicWeek**: `schedules`
- **Role**: `users`

## Cấu trúc quan hệ sau khi khắc phục

```
Department (managed) ←→ (back) Major (managed) ←→ (back) Class
    ↓ (managed)                                        ↓ (ignored)
Teacher (ignored collections)                      Schedule
    ↓ (ignored)
   User (ignored collections)

Campus (managed) ←→ (back) Room
                            ↓ (ignored)
                        Schedule

Course → (ignored) → Lessons, Schedules
TimeSlot → (ignored) → Schedules
WeekDay → (ignored) → Schedules
AcademicWeek → (ignored) → Schedules
```

## Lợi ích

1. **Không còn StackOverflowError**: Circular references đã được ngăn chặn
2. **JSON Response sạch**: Chỉ serialize dữ liệu cần thiết
3. **Performance tốt hơn**: Không load unnecessary data
4. **Security**: Password và sensitive data được ẩn
5. **API-friendly**: JSON structure phù hợp cho frontend

## Kiểm tra sau khi khắc phục

1. **Restart ứng dụng**:
```bash
mvn spring-boot:run
```

2. **Test API endpoints**:
```bash
# Get departments (should include majors and teachers)
curl -X GET http://localhost:8080/api/departments

# Get majors (should include classes but not department details)
curl -X GET http://localhost:8080/api/majors

# Get teachers (should not include schedules or user details)
curl -X GET http://localhost:8080/api/teachers
```

3. **Kiểm tra logs**: Không còn StackOverflowError

## Lưu ý quan trọng

1. **@JsonManagedReference/@JsonBackReference**: Phải được sử dụng theo cặp
2. **@JsonIgnore**: Sử dụng cho collections lớn hoặc sensitive data
3. **Lazy Loading**: Vẫn hoạt động bình thường với JPA
4. **API Design**: Có thể cần tạo separate DTOs cho complex responses

## Files đã cập nhật

```
src/main/java/com/smartscheduler/model/
├── Department.java     ✅ Added @JsonManagedReference
├── Major.java          ✅ Added @JsonBackReference/@JsonManagedReference
├── Class.java          ✅ Added @JsonBackReference/@JsonIgnore
├── Teacher.java        ✅ Added @JsonBackReference/@JsonIgnore
├── Course.java         ✅ Added @JsonIgnore
├── Campus.java         ✅ Added @JsonManagedReference
├── Room.java           ✅ Added @JsonBackReference/@JsonIgnore
├── User.java           ✅ Added @JsonIgnore
├── Role.java           ✅ Added @JsonIgnore
├── TimeSlot.java       ✅ Added @JsonIgnore
├── WeekDay.java        ✅ Added @JsonIgnore
└── AcademicWeek.java   ✅ Added @JsonIgnore
```

Sau khi áp dụng các thay đổi này, lỗi StackOverflowError sẽ được khắc phục hoàn toàn và API sẽ trả về JSON responses hợp lệ!
