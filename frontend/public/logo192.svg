<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 192 192" width="192" height="192">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976d2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1565c0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="96" cy="96" r="90" fill="url(#grad1)" stroke="#0d47a1" stroke-width="6"/>
  
  <!-- Calendar icon -->
  <rect x="48" y="60" width="96" height="72" rx="12" fill="white" stroke="none"/>
  <rect x="48" y="60" width="96" height="24" rx="12" fill="#e3f2fd"/>
  
  <!-- Calendar grid -->
  <g stroke="#1976d2" stroke-width="3" fill="none">
    <line x1="66" y1="84" x2="66" y2="120"/>
    <line x1="84" y1="84" x2="84" y2="120"/>
    <line x1="102" y1="84" x2="102" y2="120"/>
    <line x1="120" y1="84" x2="120" y2="120"/>
    
    <line x1="48" y1="96" x2="144" y2="96"/>
    <line x1="48" y1="108" x2="144" y2="108"/>
  </g>
  
  <!-- Calendar rings -->
  <rect x="60" y="48" width="6" height="24" fill="#666"/>
  <rect x="126" y="48" width="6" height="24" fill="#666"/>
  
  <!-- Smart indicator -->
  <circle cx="114" cy="102" r="9" fill="#4caf50"/>
  
  <!-- Text -->
  <text x="96" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">SmartScheduler</text>
</svg>
