<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976d2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1565c0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#grad1)" stroke="#0d47a1" stroke-width="1"/>
  
  <!-- Calendar icon -->
  <rect x="8" y="10" width="16" height="12" rx="2" fill="white" stroke="none"/>
  <rect x="8" y="10" width="16" height="4" rx="2" fill="#e3f2fd"/>
  
  <!-- Calendar grid -->
  <line x1="11" y1="14" x2="11" y2="20" stroke="#1976d2" stroke-width="0.5"/>
  <line x1="14" y1="14" x2="14" y2="20" stroke="#1976d2" stroke-width="0.5"/>
  <line x1="17" y1="14" x2="17" y2="20" stroke="#1976d2" stroke-width="0.5"/>
  <line x1="20" y1="14" x2="20" y2="20" stroke="#1976d2" stroke-width="0.5"/>
  
  <line x1="8" y1="16" x2="24" y2="16" stroke="#1976d2" stroke-width="0.5"/>
  <line x1="8" y1="18" x2="24" y2="18" stroke="#1976d2" stroke-width="0.5"/>
  
  <!-- Calendar rings -->
  <rect x="10" y="8" width="1" height="4" fill="#666"/>
  <rect x="21" y="8" width="1" height="4" fill="#666"/>
  
  <!-- Smart indicator (small dot) -->
  <circle cx="19" cy="17" r="1.5" fill="#4caf50"/>
</svg>
