<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976d2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1565c0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#grad1)" stroke="#0d47a1" stroke-width="16"/>
  
  <!-- Calendar icon -->
  <rect x="128" y="160" width="256" height="192" rx="32" fill="white" stroke="none"/>
  <rect x="128" y="160" width="256" height="64" rx="32" fill="#e3f2fd"/>
  
  <!-- Calendar grid -->
  <g stroke="#1976d2" stroke-width="8" fill="none">
    <line x1="176" y1="224" x2="176" y2="320"/>
    <line x1="224" y1="224" x2="224" y2="320"/>
    <line x1="272" y1="224" x2="272" y2="320"/>
    <line x1="320" y1="224" x2="320" y2="320"/>
    
    <line x1="128" y1="256" x2="384" y2="256"/>
    <line x1="128" y1="288" x2="384" y2="288"/>
  </g>
  
  <!-- Calendar rings -->
  <rect x="160" y="128" width="16" height="64" fill="#666"/>
  <rect x="336" y="128" width="16" height="64" fill="#666"/>
  
  <!-- Smart indicator -->
  <circle cx="304" cy="272" r="24" fill="#4caf50"/>
  
  <!-- Text -->
  <text x="256" y="420" text-anchor="middle" font-family="Arial, sans-serif" font-size="42" font-weight="bold" fill="white">SmartScheduler</text>
  <text x="256" y="460" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="#e3f2fd">Educational Scheduling System</text>
</svg>
