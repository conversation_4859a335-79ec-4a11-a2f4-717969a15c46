.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.content-container {
  display: flex;
  flex: 1;
}

.main-content {
  flex: 1;
  padding: 0;
  margin-left: 240px; /* Width of sidebar */
  min-height: calc(100vh - 64px); /* Height minus header */
  background-color: #f5f5f5;
}

@media (max-width: 600px) {
  .main-content {
    margin-left: 0;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Schedule grid styles */
.schedule-grid {
  width: 100%;
  border-collapse: collapse;
}

.schedule-grid th,
.schedule-grid td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
  vertical-align: top;
}

.schedule-grid th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.schedule-cell {
  min-height: 80px;
  position: relative;
}

.schedule-item {
  background-color: #e3f2fd;
  border-radius: 4px;
  padding: 4px;
  margin: 2px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.schedule-item:hover {
  background-color: #bbdefb;
}

.schedule-item.theory {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.schedule-item.practice {
  background-color: #e8f5e9;
  border-left: 4px solid #4caf50;
}

/* Loading spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Form styles */
.form-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.form-field {
  margin-bottom: 16px;
}

/* Card hover effects */
.hover-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

/* Data grid custom styles */
.data-grid-container {
  height: 500px;
  width: 100%;
}

.data-grid-container .MuiDataGrid-root {
  border: none;
}

.data-grid-container .MuiDataGrid-cell {
  border-bottom: 1px solid #f0f0f0;
}

.data-grid-container .MuiDataGrid-columnHeaders {
  background-color: #fafafa;
  border-bottom: 2px solid #e0e0e0;
}

/* Chart container */
.chart-container {
  width: 100%;
  height: 300px;
  margin: 20px 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding: 16px;
  }
  
  .schedule-grid {
    font-size: 12px;
  }
  
  .schedule-grid th,
  .schedule-grid td {
    padding: 4px;
  }
  
  .form-container {
    padding: 16px;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .schedule-grid {
    border-collapse: collapse;
  }
  
  .schedule-grid th,
  .schedule-grid td {
    border: 1px solid #000;
    padding: 8px;
  }
}
