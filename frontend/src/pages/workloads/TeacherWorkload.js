import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Divider,
  LinearProgress
} from '@mui/material';
import { 
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  School as SchoolIcon,
  Computer as ComputerIcon,
  Download as DownloadIcon,
  DateRange as DateRangeIcon
} from '@mui/icons-material';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import axios from 'axios';
import AuthService from '../../services/AuthService';

const TeacherWorkload = ({ teacherId }) => {
  const [workloads, setWorkloads] = useState([]);
  const [totalStats, setTotalStats] = useState({
    totalHours: 0,
    totalWeightedHours: 0,
    theoryHours: 0,
    practiceHours: 0
  });
  const [selectedAcademicYear, setSelectedAcademicYear] = useState('2024-2025');
  const [selectedSemester, setSelectedSemester] = useState('');
  const [chartData, setChartData] = useState([]);
  const [pieData, setPieData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const academicYears = ['2023-2024', '2024-2025', '2025-2026'];
  const semesters = [1, 2];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  const fetchWorkloads = async () => {
    try {
      setLoading(true);
      let url = `/workloads/teacher/${teacherId}`;
      
      if (selectedAcademicYear && selectedSemester) {
        url += `/academic-year/${selectedAcademicYear}`;
        // Note: In a real implementation, you'd also filter by semester
      } else if (selectedAcademicYear) {
        url += `/academic-year/${selectedAcademicYear}`;
      }
      
      const response = await axios.get(url, {
        headers: AuthService.getAuthHeader()
      });
      
      setWorkloads(response.data);
      calculateStats(response.data);
      prepareChartData(response.data);
      setLoading(false);
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };

  const fetchTotalWeightedHours = async () => {
    try {
      let url = `/workloads/teacher/${teacherId}/total/academic-year/${selectedAcademicYear}`;
      
      const response = await axios.get(url, {
        headers: AuthService.getAuthHeader()
      });
      
      setTotalStats(prev => ({
        ...prev,
        totalWeightedHours: response.data.totalWeightedHours || 0
      }));
    } catch (err) {
      console.error('Error fetching total weighted hours:', err);
    }
  };

  const calculateStats = (workloadData) => {
    const stats = workloadData.reduce((acc, workload) => {
      acc.totalHours += workload.hours || 0;
      acc.totalWeightedHours += workload.weightedHours || 0;
      
      if (workload.teachingFormat === 'THEORY') {
        acc.theoryHours += workload.hours || 0;
      } else if (workload.teachingFormat === 'PRACTICE') {
        acc.practiceHours += workload.hours || 0;
      }
      
      return acc;
    }, {
      totalHours: 0,
      totalWeightedHours: 0,
      theoryHours: 0,
      practiceHours: 0
    });

    setTotalStats(stats);
  };

  const prepareChartData = (workloadData) => {
    // Group by month for bar chart
    const monthlyData = workloadData.reduce((acc, workload) => {
      const date = new Date(workload.teachingDate);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!acc[monthKey]) {
        acc[monthKey] = {
          month: monthKey,
          theory: 0,
          practice: 0,
          totalWeighted: 0
        };
      }
      
      if (workload.teachingFormat === 'THEORY') {
        acc[monthKey].theory += workload.hours || 0;
      } else {
        acc[monthKey].practice += workload.hours || 0;
      }
      
      acc[monthKey].totalWeighted += workload.weightedHours || 0;
      
      return acc;
    }, {});

    const chartArray = Object.values(monthlyData).sort((a, b) => a.month.localeCompare(b.month));
    setChartData(chartArray);

    // Prepare pie chart data
    const pieArray = [
      { name: 'Theory Hours', value: totalStats.theoryHours, color: '#0088FE' },
      { name: 'Practice Hours', value: totalStats.practiceHours, color: '#00C49F' }
    ].filter(item => item.value > 0);
    
    setPieData(pieArray);
  };

  useEffect(() => {
    if (teacherId) {
      fetchWorkloads();
      fetchTotalWeightedHours();
    }
  }, [teacherId, selectedAcademicYear, selectedSemester]);

  useEffect(() => {
    prepareChartData(workloads);
  }, [totalStats]);

  const handleAcademicYearChange = (event) => {
    setSelectedAcademicYear(event.target.value);
  };

  const handleSemesterChange = (event) => {
    setSelectedSemester(event.target.value);
  };

  const exportWorkload = () => {
    // This would implement PDF/Excel export functionality
    console.log('Exporting workload report...');
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography>Loading...</Typography>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography color="error">Error: {error}</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>
        My Teaching Workload
      </Typography>
      
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Academic Year</InputLabel>
              <Select
                value={selectedAcademicYear}
                onChange={handleAcademicYearChange}
                label="Academic Year"
              >
                {academicYears.map((year) => (
                  <MenuItem key={year} value={year}>
                    {year}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Semester</InputLabel>
              <Select
                value={selectedSemester}
                onChange={handleSemesterChange}
                label="Semester"
              >
                <MenuItem value="">
                  <em>All Semesters</em>
                </MenuItem>
                {semesters.map((semester) => (
                  <MenuItem key={semester} value={semester}>
                    Semester {semester}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={exportWorkload}
            >
              Export Report
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card sx={{ bgcolor: '#e3f2fd' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <AssessmentIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Hours
                  </Typography>
                  <Typography variant="h4">
                    {totalStats.totalHours}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card sx={{ bgcolor: '#e8f5e9' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Weighted Hours
                  </Typography>
                  <Typography variant="h4">
                    {totalStats.totalWeightedHours.toFixed(1)}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card sx={{ bgcolor: '#fff3e0' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SchoolIcon color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Theory Hours
                  </Typography>
                  <Typography variant="h4">
                    {totalStats.theoryHours}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card sx={{ bgcolor: '#fce4ec' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ComputerIcon color="secondary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Practice Hours
                  </Typography>
                  <Typography variant="h4">
                    {totalStats.practiceHours}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Monthly Teaching Hours
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="theory" stackId="a" fill="#0088FE" name="Theory Hours" />
                <Bar dataKey="practice" stackId="a" fill="#00C49F" name="Practice Hours" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Hours Distribution
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Workload Progress */}
      <Paper sx={{ p: 2, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Academic Year Progress
        </Typography>
        <Box sx={{ mt: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">Teaching Hours Progress</Typography>
            <Typography variant="body2">{totalStats.totalHours} / 300 hours</Typography>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={Math.min((totalStats.totalHours / 300) * 100, 100)} 
            sx={{ height: 10, borderRadius: 5 }}
          />
        </Box>
        
        <Box sx={{ mt: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">Weighted Hours Progress</Typography>
            <Typography variant="body2">{totalStats.totalWeightedHours.toFixed(1)} / 360 weighted hours</Typography>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={Math.min((totalStats.totalWeightedHours / 360) * 100, 100)} 
            color="success"
            sx={{ height: 10, borderRadius: 5 }}
          />
        </Box>
      </Paper>

      {/* Summary Information */}
      <Paper sx={{ p: 2, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Summary Information
        </Typography>
        <Divider sx={{ my: 2 }} />
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Typography variant="body1" paragraph>
              <strong>Academic Year:</strong> {selectedAcademicYear}
            </Typography>
            <Typography variant="body1" paragraph>
              <strong>Total Teaching Sessions:</strong> {workloads.length}
            </Typography>
            <Typography variant="body1" paragraph>
              <strong>Average Hours per Session:</strong> {workloads.length > 0 ? (totalStats.totalHours / workloads.length).toFixed(1) : 0}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="body1" paragraph>
              <strong>Theory vs Practice Ratio:</strong> {totalStats.theoryHours}:{totalStats.practiceHours}
            </Typography>
            <Typography variant="body1" paragraph>
              <strong>Average Coefficient:</strong> {totalStats.totalHours > 0 ? (totalStats.totalWeightedHours / totalStats.totalHours).toFixed(2) : 0}
            </Typography>
            <Typography variant="body1" paragraph>
              <strong>Report Period:</strong> {selectedSemester ? `Semester ${selectedSemester}` : 'Full Academic Year'}
            </Typography>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default TeacherWorkload;
