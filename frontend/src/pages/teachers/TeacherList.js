import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Snackbar,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import axios from 'axios';
import AuthService from '../../services/AuthService';

const TeacherList = () => {
  const [teachers, setTeachers] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [currentTeacher, setCurrentTeacher] = useState({
    id: null,
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    teacherCode: '',
    department: { id: '' }
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  const fetchTeachers = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/teachers', {
        headers: AuthService.getAuthHeader()
      });
      setTeachers(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching teachers:', error);
      setSnackbar({
        open: true,
        message: 'Error fetching teachers: ' + (error.response?.data?.message || error.message),
        severity: 'error'
      });
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const response = await axios.get('/departments', {
        headers: AuthService.getAuthHeader()
      });
      setDepartments(response.data);
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  useEffect(() => {
    fetchTeachers();
    fetchDepartments();
  }, []);

  const handleOpenDialog = (teacher = null) => {
    if (teacher) {
      setCurrentTeacher({
        ...teacher,
        department: teacher.department || { id: '' }
      });
    } else {
      setCurrentTeacher({
        id: null,
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        teacherCode: '',
        department: { id: '' }
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleOpenDeleteDialog = (teacher) => {
    setCurrentTeacher(teacher);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === 'departmentId') {
      setCurrentTeacher({
        ...currentTeacher,
        department: { id: value }
      });
    } else {
      setCurrentTeacher({
        ...currentTeacher,
        [name]: value
      });
    }
  };

  const handleSaveTeacher = async () => {
    try {
      const teacherData = {
        ...currentTeacher,
        department: currentTeacher.department.id ? { id: currentTeacher.department.id } : null
      };

      if (currentTeacher.id) {
        // Update existing teacher
        await axios.put(`/teachers/${currentTeacher.id}`, teacherData, {
          headers: AuthService.getAuthHeader()
        });
        setSnackbar({
          open: true,
          message: 'Teacher updated successfully',
          severity: 'success'
        });
      } else {
        // Create new teacher
        await axios.post('/teachers', teacherData, {
          headers: AuthService.getAuthHeader()
        });
        setSnackbar({
          open: true,
          message: 'Teacher created successfully',
          severity: 'success'
        });
      }
      handleCloseDialog();
      fetchTeachers();
    } catch (error) {
      console.error('Error saving teacher:', error);
      setSnackbar({
        open: true,
        message: 'Error: ' + (error.response?.data?.message || error.message),
        severity: 'error'
      });
    }
  };

  const handleDeleteTeacher = async () => {
    try {
      await axios.delete(`/teachers/${currentTeacher.id}`, {
        headers: AuthService.getAuthHeader()
      });
      setSnackbar({
        open: true,
        message: 'Teacher deleted successfully',
        severity: 'success'
      });
      handleCloseDeleteDialog();
      fetchTeachers();
    } catch (error) {
      console.error('Error deleting teacher:', error);
      setSnackbar({
        open: true,
        message: 'Error: ' + (error.response?.data?.message || error.message),
        severity: 'error'
      });
      handleCloseDeleteDialog();
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  const columns = [
    { field: 'id', headerName: 'ID', width: 70 },
    { field: 'teacherCode', headerName: 'Code', width: 120 },
    { field: 'firstName', headerName: 'First Name', width: 150 },
    { field: 'lastName', headerName: 'Last Name', width: 150 },
    { field: 'email', headerName: 'Email', width: 200 },
    { field: 'phone', headerName: 'Phone', width: 130 },
    {
      field: 'department',
      headerName: 'Department',
      width: 150,
      valueGetter: (params) => params.row.department?.name || 'N/A'
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      renderCell: (params) => (
        <>
          <IconButton
            color="primary"
            onClick={() => handleOpenDialog(params.row)}
            size="small"
          >
            <EditIcon />
          </IconButton>
          <IconButton
            color="error"
            onClick={() => handleOpenDeleteDialog(params.row)}
            size="small"
          >
            <DeleteIcon />
          </IconButton>
        </>
      )
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>
        Teachers
      </Typography>

      <Paper sx={{ p: 2, mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add Teacher
        </Button>
      </Paper>

      <Paper sx={{ p: 2, height: 600 }}>
        <DataGrid
          rows={teachers}
          columns={columns}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 10 },
            },
          }}
          pageSizeOptions={[10, 25, 50]}
          loading={loading}
          disableRowSelectionOnClick
        />
      </Paper>

      {/* Add/Edit Teacher Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {currentTeacher.id ? 'Edit Teacher' : 'Add Teacher'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="teacherCode"
            label="Teacher Code"
            type="text"
            fullWidth
            value={currentTeacher.teacherCode}
            onChange={handleInputChange}
            required
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="firstName"
            label="First Name"
            type="text"
            fullWidth
            value={currentTeacher.firstName}
            onChange={handleInputChange}
            required
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="lastName"
            label="Last Name"
            type="text"
            fullWidth
            value={currentTeacher.lastName}
            onChange={handleInputChange}
            required
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="email"
            label="Email"
            type="email"
            fullWidth
            value={currentTeacher.email}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="phone"
            label="Phone"
            type="text"
            fullWidth
            value={currentTeacher.phone}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Department</InputLabel>
            <Select
              name="departmentId"
              value={currentTeacher.department?.id || ''}
              onChange={handleInputChange}
              label="Department"
            >
              <MenuItem value="">
                <em>Select Department</em>
              </MenuItem>
              {departments.map((dept) => (
                <MenuItem key={dept.id} value={dept.id}>
                  {dept.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSaveTeacher} variant="contained" color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete teacher "{currentTeacher.firstName} {currentTeacher.lastName}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDeleteTeacher} variant="contained" color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default TeacherList;
