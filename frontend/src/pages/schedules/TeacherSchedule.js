import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Box,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import { 
  School as TheoryIcon, 
  Computer as PracticeIcon,
  Download as DownloadIcon 
} from '@mui/icons-material';
import axios from 'axios';
import AuthService from '../../services/AuthService';

const TeacherSchedule = ({ teacherId }) => {
  const [schedules, setSchedules] = useState([]);
  const [selectedWeek, setSelectedWeek] = useState('');
  const [academicWeeks, setAcademicWeeks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Time slots for the schedule grid
  const timeSlots = [
    { id: 1, name: 'Tiết 1-2', time: '7:00-8:30' },
    { id: 2, name: 'Tiết 3-4', time: '8:45-10:15' },
    { id: 3, name: 'Tiết 5-6', time: '10:30-12:00' },
    { id: 4, name: 'Tiết 7-8', time: '13:00-14:30' },
    { id: 5, name: 'Tiết 9-10', time: '14:45-16:15' },
    { id: 6, name: 'Tiết 11-12', time: '16:30-18:00' },
    { id: 7, name: 'Tiết 13-14', time: '18:15-19:45' }
  ];

  const weekDays = [
    { id: 1, name: 'Thứ 2', dayOrder: 1 },
    { id: 2, name: 'Thứ 3', dayOrder: 2 },
    { id: 3, name: 'Thứ 4', dayOrder: 3 },
    { id: 4, name: 'Thứ 5', dayOrder: 4 },
    { id: 5, name: 'Thứ 6', dayOrder: 5 },
    { id: 6, name: 'Thứ 7', dayOrder: 6 },
    { id: 7, name: 'Chủ nhật', dayOrder: 7 }
  ];

  const fetchSchedules = async () => {
    try {
      setLoading(true);
      let url = `/schedules/teacher/${teacherId}`;
      if (selectedWeek) {
        url += `/week/${selectedWeek}`;
      }
      
      const response = await axios.get(url, {
        headers: AuthService.getAuthHeader()
      });
      setSchedules(response.data);
      setLoading(false);
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };

  const fetchAcademicWeeks = async () => {
    try {
      // Mock data for academic weeks
      setAcademicWeeks([
        { id: 1, weekNumber: 1, academicYear: '2024-2025', semester: 1, startDate: '2024-09-02' },
        { id: 2, weekNumber: 2, academicYear: '2024-2025', semester: 1, startDate: '2024-09-09' },
        { id: 3, weekNumber: 3, academicYear: '2024-2025', semester: 1, startDate: '2024-09-16' },
        { id: 4, weekNumber: 4, academicYear: '2024-2025', semester: 1, startDate: '2024-09-23' },
        { id: 5, weekNumber: 5, academicYear: '2024-2025', semester: 1, startDate: '2024-09-30' }
      ]);
    } catch (err) {
      console.error('Error fetching academic weeks:', err);
    }
  };

  useEffect(() => {
    if (teacherId) {
      fetchSchedules();
      fetchAcademicWeeks();
    }
  }, [teacherId, selectedWeek]);

  const handleWeekChange = (event) => {
    setSelectedWeek(event.target.value);
  };

  const getScheduleForSlot = (dayOrder, timeSlotId) => {
    return schedules.find(schedule => 
      schedule.weekDay?.dayOrder === dayOrder && 
      schedule.timeSlot?.id === timeSlotId
    );
  };

  const exportSchedule = () => {
    // This would implement PDF/Excel export functionality
    console.log('Exporting schedule...');
  };

  const renderScheduleCell = (schedule) => {
    if (!schedule) {
      return <TableCell key={Math.random()}></TableCell>;
    }

    return (
      <TableCell key={schedule.id} sx={{ p: 1, border: '1px solid #ddd' }}>
        <Card 
          sx={{ 
            minHeight: 80, 
            bgcolor: schedule.teachingFormat === 'THEORY' ? '#e3f2fd' : '#e8f5e9',
            cursor: 'pointer'
          }}
        >
          <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              {schedule.teachingFormat === 'THEORY' ? 
                <TheoryIcon fontSize="small" color="primary" /> : 
                <PracticeIcon fontSize="small" color="success" />
              }
              <Chip 
                label={schedule.teachingFormat === 'THEORY' ? 'LT' : 'TH'} 
                size="small" 
                sx={{ ml: 0.5 }}
                color={schedule.teachingFormat === 'THEORY' ? 'primary' : 'success'}
              />
            </Box>
            <Typography variant="body2" fontWeight="bold" noWrap>
              {schedule.course?.name}
            </Typography>
            <Typography variant="caption" display="block" noWrap>
              {schedule.course?.code}
            </Typography>
            <Typography variant="caption" display="block" noWrap>
              Lớp: {schedule.classEntity?.name}
            </Typography>
            <Typography variant="caption" display="block" noWrap>
              Phòng: {schedule.room?.name}
            </Typography>
            {schedule.practiceGroup && (
              <Typography variant="caption" display="block" noWrap>
                Nhóm TH: {schedule.practiceGroup}
              </Typography>
            )}
            <Typography variant="caption" display="block" color="text.secondary">
              {schedule.hours}h (x{schedule.coefficient})
            </Typography>
          </CardContent>
        </Card>
      </TableCell>
    );
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Typography>Loading...</Typography>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Typography color="error">Error: {error}</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>
        My Teaching Schedule
      </Typography>
      
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Academic Week</InputLabel>
              <Select
                value={selectedWeek}
                onChange={handleWeekChange}
                label="Academic Week"
              >
                <MenuItem value="">
                  <em>All Weeks</em>
                </MenuItem>
                {academicWeeks.map((week) => (
                  <MenuItem key={week.id} value={week.id}>
                    Week {week.weekNumber} - {week.academicYear} (Semester {week.semester})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={8} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={exportSchedule}
            >
              Export Schedule
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Weekly Schedule
        </Typography>
        
        <TableContainer>
          <Table sx={{ minWidth: 800 }}>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold', minWidth: 120 }}>Time</TableCell>
                {weekDays.map((day) => (
                  <TableCell key={day.id} align="center" sx={{ fontWeight: 'bold', minWidth: 150 }}>
                    {day.name}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {timeSlots.map((timeSlot) => (
                <TableRow key={timeSlot.id}>
                  <TableCell sx={{ fontWeight: 'bold', bgcolor: '#f5f5f5' }}>
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {timeSlot.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {timeSlot.time}
                      </Typography>
                    </Box>
                  </TableCell>
                  {weekDays.map((day) => 
                    renderScheduleCell(getScheduleForSlot(day.dayOrder, timeSlot.id))
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {schedules.length === 0 && (
        <Paper sx={{ p: 4, mt: 2, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            No schedules found for the selected period.
          </Typography>
        </Paper>
      )}

      <Paper sx={{ p: 2, mt: 2 }}>
        <Typography variant="h6" gutterBottom>
          Legend
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <TheoryIcon color="primary" sx={{ mr: 1 }} />
              <Chip label="LT" size="small" color="primary" sx={{ mr: 1 }} />
              <Typography variant="body2">Theory Classes (Lý thuyết)</Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <PracticeIcon color="success" sx={{ mr: 1 }} />
              <Chip label="TH" size="small" color="success" sx={{ mr: 1 }} />
              <Typography variant="body2">Practice Classes (Thực hành)</Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default TeacherSchedule;
