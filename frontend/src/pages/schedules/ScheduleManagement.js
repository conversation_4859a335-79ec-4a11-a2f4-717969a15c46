import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Snackbar,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Box
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Visibility as ViewIcon } from '@mui/icons-material';
import axios from 'axios';
import AuthService from '../../services/AuthService';

const ScheduleManagement = () => {
  const [schedules, setSchedules] = useState([]);
  const [teachers, setTeachers] = useState([]);
  const [courses, setCourses] = useState([]);
  const [classes, setClasses] = useState([]);
  const [rooms, setRooms] = useState([]);
  const [timeSlots, setTimeSlots] = useState([]);
  const [weekDays, setWeekDays] = useState([]);
  const [academicWeeks, setAcademicWeeks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [currentSchedule, setCurrentSchedule] = useState({
    id: null,
    teacher: { id: '' },
    course: { id: '' },
    classEntity: { id: '' },
    room: { id: '' },
    timeSlot: { id: '' },
    weekDay: { id: '' },
    academicWeek: { id: '' },
    teachingFormat: 'THEORY',
    practiceGroup: '',
    hours: 1,
    coefficient: 1.0,
    notes: ''
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  const fetchSchedules = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/schedules', {
        headers: AuthService.getAuthHeader()
      });
      setSchedules(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching schedules:', error);
      setSnackbar({
        open: true,
        message: 'Error fetching schedules: ' + (error.response?.data?.message || error.message),
        severity: 'error'
      });
      setLoading(false);
    }
  };

  const fetchReferenceData = async () => {
    try {
      const [teachersRes, coursesRes, classesRes, roomsRes] = await Promise.all([
        axios.get('/teachers', { headers: AuthService.getAuthHeader() }),
        axios.get('/courses', { headers: AuthService.getAuthHeader() }),
        axios.get('/classes', { headers: AuthService.getAuthHeader() }),
        axios.get('/rooms', { headers: AuthService.getAuthHeader() })
      ]);

      setTeachers(teachersRes.data);
      setCourses(coursesRes.data);
      setClasses(classesRes.data);
      setRooms(roomsRes.data);

      // Mock data for time slots, week days, and academic weeks
      // In a real application, these would come from the backend
      setTimeSlots([
        { id: 1, name: 'Tiết 1-2 (7:00-8:30)', session: 'MORNING' },
        { id: 2, name: 'Tiết 3-4 (8:45-10:15)', session: 'MORNING' },
        { id: 3, name: 'Tiết 5-6 (10:30-12:00)', session: 'MORNING' },
        { id: 4, name: 'Tiết 7-8 (13:00-14:30)', session: 'AFTERNOON' },
        { id: 5, name: 'Tiết 9-10 (14:45-16:15)', session: 'AFTERNOON' },
        { id: 6, name: 'Tiết 11-12 (16:30-18:00)', session: 'AFTERNOON' },
        { id: 7, name: 'Tiết 13-14 (18:15-19:45)', session: 'EVENING' }
      ]);

      setWeekDays([
        { id: 1, name: 'Thứ 2', dayOrder: 1 },
        { id: 2, name: 'Thứ 3', dayOrder: 2 },
        { id: 3, name: 'Thứ 4', dayOrder: 3 },
        { id: 4, name: 'Thứ 5', dayOrder: 4 },
        { id: 5, name: 'Thứ 6', dayOrder: 5 },
        { id: 6, name: 'Thứ 7', dayOrder: 6 },
        { id: 7, name: 'Chủ nhật', dayOrder: 7 }
      ]);

      setAcademicWeeks([
        { id: 1, weekNumber: 1, academicYear: '2024-2025', semester: 1 },
        { id: 2, weekNumber: 2, academicYear: '2024-2025', semester: 1 },
        { id: 3, weekNumber: 3, academicYear: '2024-2025', semester: 1 }
      ]);

    } catch (error) {
      console.error('Error fetching reference data:', error);
    }
  };

  useEffect(() => {
    fetchSchedules();
    fetchReferenceData();
  }, []);

  const handleOpenDialog = (schedule = null) => {
    if (schedule) {
      setCurrentSchedule({
        ...schedule,
        teacher: schedule.teacher || { id: '' },
        course: schedule.course || { id: '' },
        classEntity: schedule.classEntity || { id: '' },
        room: schedule.room || { id: '' },
        timeSlot: schedule.timeSlot || { id: '' },
        weekDay: schedule.weekDay || { id: '' },
        academicWeek: schedule.academicWeek || { id: '' }
      });
    } else {
      setCurrentSchedule({
        id: null,
        teacher: { id: '' },
        course: { id: '' },
        classEntity: { id: '' },
        room: { id: '' },
        timeSlot: { id: '' },
        weekDay: { id: '' },
        academicWeek: { id: '' },
        teachingFormat: 'THEORY',
        practiceGroup: '',
        hours: 1,
        coefficient: 1.0,
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleOpenDeleteDialog = (schedule) => {
    setCurrentSchedule(schedule);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setCurrentSchedule({
        ...currentSchedule,
        [parent]: { id: value }
      });
    } else {
      setCurrentSchedule({
        ...currentSchedule,
        [name]: value
      });
    }
  };

  const handleSaveSchedule = async () => {
    try {
      const scheduleData = {
        ...currentSchedule,
        teacher: currentSchedule.teacher.id ? { id: currentSchedule.teacher.id } : null,
        course: currentSchedule.course.id ? { id: currentSchedule.course.id } : null,
        classEntity: currentSchedule.classEntity.id ? { id: currentSchedule.classEntity.id } : null,
        room: currentSchedule.room.id ? { id: currentSchedule.room.id } : null,
        timeSlot: currentSchedule.timeSlot.id ? { id: currentSchedule.timeSlot.id } : null,
        weekDay: currentSchedule.weekDay.id ? { id: currentSchedule.weekDay.id } : null,
        academicWeek: currentSchedule.academicWeek.id ? { id: currentSchedule.academicWeek.id } : null
      };

      if (currentSchedule.id) {
        // Update existing schedule
        await axios.put(`/schedules/${currentSchedule.id}`, scheduleData, {
          headers: AuthService.getAuthHeader()
        });
        setSnackbar({
          open: true,
          message: 'Schedule updated successfully',
          severity: 'success'
        });
      } else {
        // Create new schedule
        await axios.post('/schedules', scheduleData, {
          headers: AuthService.getAuthHeader()
        });
        setSnackbar({
          open: true,
          message: 'Schedule created successfully',
          severity: 'success'
        });
      }
      handleCloseDialog();
      fetchSchedules();
    } catch (error) {
      console.error('Error saving schedule:', error);
      setSnackbar({
        open: true,
        message: 'Error: ' + (error.response?.data?.message || error.message),
        severity: 'error'
      });
    }
  };

  const handleDeleteSchedule = async () => {
    try {
      await axios.delete(`/schedules/${currentSchedule.id}`, {
        headers: AuthService.getAuthHeader()
      });
      setSnackbar({
        open: true,
        message: 'Schedule deleted successfully',
        severity: 'success'
      });
      handleCloseDeleteDialog();
      fetchSchedules();
    } catch (error) {
      console.error('Error deleting schedule:', error);
      setSnackbar({
        open: true,
        message: 'Error: ' + (error.response?.data?.message || error.message),
        severity: 'error'
      });
      handleCloseDeleteDialog();
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  const columns = [
    { field: 'id', headerName: 'ID', width: 70 },
    {
      field: 'teacher',
      headerName: 'Teacher',
      width: 150,
      valueGetter: (params) => params.row.teacher?.firstName + ' ' + params.row.teacher?.lastName || 'N/A'
    },
    {
      field: 'course',
      headerName: 'Course',
      width: 150,
      valueGetter: (params) => params.row.course?.name || 'N/A'
    },
    {
      field: 'class',
      headerName: 'Class',
      width: 120,
      valueGetter: (params) => params.row.classEntity?.name || 'N/A'
    },
    {
      field: 'room',
      headerName: 'Room',
      width: 100,
      valueGetter: (params) => params.row.room?.name || 'N/A'
    },
    {
      field: 'weekDay',
      headerName: 'Day',
      width: 100,
      valueGetter: (params) => params.row.weekDay?.name || 'N/A'
    },
    {
      field: 'timeSlot',
      headerName: 'Time',
      width: 150,
      valueGetter: (params) => params.row.timeSlot?.name || 'N/A'
    },
    { field: 'teachingFormat', headerName: 'Format', width: 100 },
    { field: 'hours', headerName: 'Hours', width: 80 },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      renderCell: (params) => (
        <>
          <IconButton
            color="primary"
            onClick={() => handleOpenDialog(params.row)}
            size="small"
          >
            <EditIcon />
          </IconButton>
          <IconButton
            color="error"
            onClick={() => handleOpenDeleteDialog(params.row)}
            size="small"
          >
            <DeleteIcon />
          </IconButton>
        </>
      )
    }
  ];

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>
        Schedule Management
      </Typography>

      <Paper sx={{ p: 2, mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add Schedule
        </Button>
      </Paper>

      <Paper sx={{ p: 2, height: 600 }}>
        <DataGrid
          rows={schedules}
          columns={columns}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 10 },
            },
          }}
          pageSizeOptions={[10, 25, 50]}
          loading={loading}
          disableRowSelectionOnClick
        />
      </Paper>

      {/* Add/Edit Schedule Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {currentSchedule.id ? 'Edit Schedule' : 'Add Schedule'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Teacher</InputLabel>
                <Select
                  name="teacher.id"
                  value={currentSchedule.teacher?.id || ''}
                  onChange={handleInputChange}
                  label="Teacher"
                >
                  <MenuItem value="">
                    <em>Select Teacher</em>
                  </MenuItem>
                  {teachers.map((teacher) => (
                    <MenuItem key={teacher.id} value={teacher.id}>
                      {teacher.firstName} {teacher.lastName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Course</InputLabel>
                <Select
                  name="course.id"
                  value={currentSchedule.course?.id || ''}
                  onChange={handleInputChange}
                  label="Course"
                >
                  <MenuItem value="">
                    <em>Select Course</em>
                  </MenuItem>
                  {courses.map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.name} ({course.code})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Class</InputLabel>
                <Select
                  name="classEntity.id"
                  value={currentSchedule.classEntity?.id || ''}
                  onChange={handleInputChange}
                  label="Class"
                >
                  <MenuItem value="">
                    <em>Select Class</em>
                  </MenuItem>
                  {classes.map((classItem) => (
                    <MenuItem key={classItem.id} value={classItem.id}>
                      {classItem.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Room</InputLabel>
                <Select
                  name="room.id"
                  value={currentSchedule.room?.id || ''}
                  onChange={handleInputChange}
                  label="Room"
                >
                  <MenuItem value="">
                    <em>Select Room</em>
                  </MenuItem>
                  {rooms.map((room) => (
                    <MenuItem key={room.id} value={room.id}>
                      {room.name} ({room.code})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Week Day</InputLabel>
                <Select
                  name="weekDay.id"
                  value={currentSchedule.weekDay?.id || ''}
                  onChange={handleInputChange}
                  label="Week Day"
                >
                  <MenuItem value="">
                    <em>Select Day</em>
                  </MenuItem>
                  {weekDays.map((day) => (
                    <MenuItem key={day.id} value={day.id}>
                      {day.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Time Slot</InputLabel>
                <Select
                  name="timeSlot.id"
                  value={currentSchedule.timeSlot?.id || ''}
                  onChange={handleInputChange}
                  label="Time Slot"
                >
                  <MenuItem value="">
                    <em>Select Time Slot</em>
                  </MenuItem>
                  {timeSlots.map((slot) => (
                    <MenuItem key={slot.id} value={slot.id}>
                      {slot.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Academic Week</InputLabel>
                <Select
                  name="academicWeek.id"
                  value={currentSchedule.academicWeek?.id || ''}
                  onChange={handleInputChange}
                  label="Academic Week"
                >
                  <MenuItem value="">
                    <em>Select Week</em>
                  </MenuItem>
                  {academicWeeks.map((week) => (
                    <MenuItem key={week.id} value={week.id}>
                      Week {week.weekNumber} - {week.academicYear} (Semester {week.semester})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Teaching Format</InputLabel>
                <Select
                  name="teachingFormat"
                  value={currentSchedule.teachingFormat}
                  onChange={handleInputChange}
                  label="Teaching Format"
                >
                  <MenuItem value="THEORY">Theory (LT)</MenuItem>
                  <MenuItem value="PRACTICE">Practice (TH)</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                margin="dense"
                name="hours"
                label="Hours"
                type="number"
                fullWidth
                value={currentSchedule.hours}
                onChange={handleInputChange}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                margin="dense"
                name="coefficient"
                label="Coefficient"
                type="number"
                step="0.1"
                fullWidth
                value={currentSchedule.coefficient}
                onChange={handleInputChange}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                margin="dense"
                name="practiceGroup"
                label="Practice Group"
                type="number"
                fullWidth
                value={currentSchedule.practiceGroup}
                onChange={handleInputChange}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                margin="dense"
                name="notes"
                label="Notes"
                type="text"
                fullWidth
                multiline
                rows={3}
                value={currentSchedule.notes}
                onChange={handleInputChange}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSaveSchedule} variant="contained" color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this schedule?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDeleteSchedule} variant="contained" color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ScheduleManagement;
