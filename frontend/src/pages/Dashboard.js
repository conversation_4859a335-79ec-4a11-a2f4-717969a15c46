import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Grid, 
  Paper, 
  Typography, 
  Box, 
  Card, 
  CardContent, 
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import { 
  PeopleAlt as PeopleIcon,
  School as SchoolIcon,
  MenuBook as CourseIcon,
  MeetingRoom as RoomIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import axios from 'axios';
import AuthService from '../services/AuthService';

const Dashboard = () => {
  const [stats, setStats] = useState({
    departments: 0,
    teachers: 0,
    courses: 0,
    classes: 0,
    rooms: 0,
    schedules: 0
  });
  
  const [upcomingSchedules, setUpcomingSchedules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const currentUser = AuthService.getCurrentUser();
  const isAdmin = currentUser?.roles.includes('ROLE_ADMIN');
  const isTeacher = currentUser?.roles.includes('ROLE_TEACHER');
  const teacherId = currentUser?.teacherId;

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        
        // Fetch counts for admin dashboard
        if (isAdmin) {
          const departmentsRes = await axios.get('/departments', { headers: AuthService.getAuthHeader() });
          const teachersRes = await axios.get('/teachers', { headers: AuthService.getAuthHeader() });
          const coursesRes = await axios.get('/courses', { headers: AuthService.getAuthHeader() });
          const classesRes = await axios.get('/classes', { headers: AuthService.getAuthHeader() });
          const roomsRes = await axios.get('/rooms', { headers: AuthService.getAuthHeader() });
          const schedulesRes = await axios.get('/schedules', { headers: AuthService.getAuthHeader() });
          
          setStats({
            departments: departmentsRes.data.length,
            teachers: teachersRes.data.length,
            courses: coursesRes.data.length,
            classes: classesRes.data.length,
            rooms: roomsRes.data.length,
            schedules: schedulesRes.data.length
          });
        }
        
        // Fetch upcoming schedules for teacher
        if (isTeacher && teacherId) {
          const today = new Date();
          const weekFromNow = new Date();
          weekFromNow.setDate(today.getDate() + 7);
          
          const schedulesRes = await axios.get(`/schedules/teacher/${teacherId}`, { 
            headers: AuthService.getAuthHeader() 
          });
          
          // Filter for upcoming schedules (would be better to do this on the backend)
          const upcoming = schedulesRes.data
            .filter(schedule => {
              const scheduleDate = new Date(schedule.academicWeek.startDate);
              scheduleDate.setDate(scheduleDate.getDate() + schedule.weekDay.dayOrder - 1);
              return scheduleDate >= today && scheduleDate <= weekFromNow;
            })
            .sort((a, b) => {
              const dateA = new Date(a.academicWeek.startDate);
              dateA.setDate(dateA.getDate() + a.weekDay.dayOrder - 1);
              
              const dateB = new Date(b.academicWeek.startDate);
              dateB.setDate(dateB.getDate() + b.weekDay.dayOrder - 1);
              
              return dateA - dateB;
            })
            .slice(0, 5);
            
          setUpcomingSchedules(upcoming);
        }
        
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    fetchStats();
  }, [isAdmin, isTeacher, teacherId]);

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      
      {isAdmin && (
        <Grid container spacing={3}>
          {/* Stats Cards */}
          <Grid item xs={12} md={4}>
            <Paper
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                height: 140,
                bgcolor: '#e3f2fd'
              }}
            >
              <Typography component="h2" variant="h6" color="primary" gutterBottom>
                Departments & Teachers
              </Typography>
              <Typography component="p" variant="h4">
                {stats.departments} / {stats.teachers}
              </Typography>
              <Typography color="text.secondary" sx={{ flex: 1 }}>
                Total departments / teachers
              </Typography>
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Paper
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                height: 140,
                bgcolor: '#e8f5e9'
              }}
            >
              <Typography component="h2" variant="h6" color="primary" gutterBottom>
                Courses & Classes
              </Typography>
              <Typography component="p" variant="h4">
                {stats.courses} / {stats.classes}
              </Typography>
              <Typography color="text.secondary" sx={{ flex: 1 }}>
                Total courses / classes
              </Typography>
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Paper
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                height: 140,
                bgcolor: '#fff8e1'
              }}
            >
              <Typography component="h2" variant="h6" color="primary" gutterBottom>
                Rooms & Schedules
              </Typography>
              <Typography component="p" variant="h4">
                {stats.rooms} / {stats.schedules}
              </Typography>
              <Typography color="text.secondary" sx={{ flex: 1 }}>
                Total rooms / scheduled sessions
              </Typography>
            </Paper>
          </Grid>
          
          {/* Recent Activity */}
          <Grid item xs={12}>
            <Paper sx={{ p: 2 }}>
              <Typography component="h2" variant="h6" color="primary" gutterBottom>
                System Overview
              </Typography>
              <Divider sx={{ my: 1 }} />
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Quick Links" />
                    <CardContent>
                      <List>
                        <ListItem button component="a" href="/schedules">
                          <ListItemIcon>
                            <CalendarIcon />
                          </ListItemIcon>
                          <ListItemText primary="Manage Schedules" />
                        </ListItem>
                        <ListItem button component="a" href="/teachers">
                          <ListItemIcon>
                            <PeopleIcon />
                          </ListItemIcon>
                          <ListItemText primary="Manage Teachers" />
                        </ListItem>
                        <ListItem button component="a" href="/courses">
                          <ListItemIcon>
                            <CourseIcon />
                          </ListItemIcon>
                          <ListItemText primary="Manage Courses" />
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="System Status" />
                    <CardContent>
                      <Typography variant="body1" paragraph>
                        The Smart Scheduler system is running normally.
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Last updated: {new Date().toLocaleString()}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      )}
      
      {isTeacher && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper sx={{ p: 2 }}>
              <Typography component="h2" variant="h6" color="primary" gutterBottom>
                Upcoming Classes
              </Typography>
              <Divider sx={{ my: 1 }} />
              
              {loading ? (
                <Typography>Loading...</Typography>
              ) : error ? (
                <Typography color="error">Error: {error}</Typography>
              ) : upcomingSchedules.length > 0 ? (
                <List>
                  {upcomingSchedules.map((schedule) => (
                    <ListItem key={schedule.id}>
                      <ListItemIcon>
                        {schedule.teachingFormat === 'THEORY' ? <SchoolIcon /> : <RoomIcon />}
                      </ListItemIcon>
                      <ListItemText
                        primary={`${schedule.course.name} (${schedule.course.code})`}
                        secondary={`${schedule.weekDay.name}, ${schedule.timeSlot.name} - ${schedule.room.name} - ${schedule.classEntity.name}`}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography>No upcoming classes in the next 7 days.</Typography>
              )}
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Typography component="h2" variant="h6" color="primary" gutterBottom>
                Quick Links
              </Typography>
              <Divider sx={{ my: 1 }} />
              <List>
                <ListItem button component="a" href="/my-schedule">
                  <ListItemIcon>
                    <CalendarIcon />
                  </ListItemIcon>
                  <ListItemText primary="View My Schedule" />
                </ListItem>
                <ListItem button component="a" href="/my-workload">
                  <ListItemIcon>
                    <SchoolIcon />
                  </ListItemIcon>
                  <ListItemText primary="View My Workload" />
                </ListItem>
              </List>
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Typography component="h2" variant="h6" color="primary" gutterBottom>
                Teaching Summary
              </Typography>
              <Divider sx={{ my: 1 }} />
              <Box sx={{ mt: 2 }}>
                <Typography variant="body1">
                  View your complete teaching schedule and workload statistics using the quick links.
                </Typography>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      )}
    </Container>
  );
};

export default Dashboard;
