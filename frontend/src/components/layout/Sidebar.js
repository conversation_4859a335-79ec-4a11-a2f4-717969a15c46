import React from 'react';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  Typography
} from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import SchoolIcon from '@mui/icons-material/School';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import AssessmentIcon from '@mui/icons-material/Assessment';
import BusinessIcon from '@mui/icons-material/Business';

const drawerWidth = 240;

const Sidebar = ({ isAdmin, isTeacher }) => {
  const location = useLocation();

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          top: '64px',
          height: 'calc(100% - 64px)'
        },
      }}
    >
      <Box sx={{ overflow: 'auto' }}>
        <List>
          <ListItem 
            button 
            component={RouterLink} 
            to="/" 
            selected={isActive('/')}
          >
            <ListItemIcon>
              <DashboardIcon />
            </ListItemIcon>
            <ListItemText primary="Dashboard" />
          </ListItem>
        </List>

        {isAdmin && (
          <>
            <Divider />
            <Box sx={{ px: 2, py: 1 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Administration
              </Typography>
            </Box>
            <List>
              <ListItem 
                button 
                component={RouterLink} 
                to="/departments" 
                selected={isActive('/departments')}
              >
                <ListItemIcon>
                  <BusinessIcon />
                </ListItemIcon>
                <ListItemText primary="Departments" />
              </ListItem>
              
              <ListItem 
                button 
                component={RouterLink} 
                to="/teachers" 
                selected={isActive('/teachers')}
              >
                <ListItemIcon>
                  <PeopleIcon />
                </ListItemIcon>
                <ListItemText primary="Teachers" />
              </ListItem>
              
              <ListItem 
                button 
                component={RouterLink} 
                to="/schedules" 
                selected={isActive('/schedules')}
              >
                <ListItemIcon>
                  <CalendarMonthIcon />
                </ListItemIcon>
                <ListItemText primary="Schedule Management" />
              </ListItem>
            </List>
          </>
        )}

        {(isAdmin || isTeacher) && (
          <>
            <Divider />
            <Box sx={{ px: 2, py: 1 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Teacher Portal
              </Typography>
            </Box>
            <List>
              <ListItem 
                button 
                component={RouterLink} 
                to="/my-schedule" 
                selected={isActive('/my-schedule')}
              >
                <ListItemIcon>
                  <SchoolIcon />
                </ListItemIcon>
                <ListItemText primary="My Schedule" />
              </ListItem>
              
              <ListItem 
                button 
                component={RouterLink} 
                to="/my-workload" 
                selected={isActive('/my-workload')}
              >
                <ListItemIcon>
                  <AssessmentIcon />
                </ListItemIcon>
                <ListItemText primary="My Workload" />
              </ListItem>
            </List>
          </>
        )}
      </Box>
    </Drawer>
  );
};

export default Sidebar;
