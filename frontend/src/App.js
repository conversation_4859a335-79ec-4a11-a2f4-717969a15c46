import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

// Components
import Header from './components/layout/Header';
import Sidebar from './components/layout/Sidebar';
import Footer from './components/layout/Footer';

// Pages
import Login from './pages/auth/Login';
import Dashboard from './pages/Dashboard';
import DepartmentList from './pages/departments/DepartmentList';
import TeacherList from './pages/teachers/TeacherList';
import ScheduleManagement from './pages/schedules/ScheduleManagement';
import TeacherSchedule from './pages/schedules/TeacherSchedule';
import TeacherWorkload from './pages/workloads/TeacherWorkload';

// Services
import AuthService from './services/AuthService';

// Styles
import './App.css';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function App() {
  const [currentUser, setCurrentUser] = useState(undefined);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isTeacher, setIsTeacher] = useState(false);

  useEffect(() => {
    const user = AuthService.getCurrentUser();

    if (user) {
      setCurrentUser(user);
      setIsAdmin(user.roles.includes('ROLE_ADMIN'));
      setIsTeacher(user.roles.includes('ROLE_TEACHER'));
    }
  }, []);

  const logOut = () => {
    AuthService.logout();
    setCurrentUser(undefined);
    setIsAdmin(false);
    setIsTeacher(false);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <div className="app">
          {currentUser ? (
            <>
              <Header currentUser={currentUser} logOut={logOut} />
              <div className="content-container">
                <Sidebar isAdmin={isAdmin} isTeacher={isTeacher} />
                <main className="main-content">
                  <Routes>
                    <Route path="/" element={<Dashboard />} />
                    
                    {/* Admin Routes */}
                    {isAdmin && (
                      <>
                        <Route path="/departments" element={<DepartmentList />} />
                        <Route path="/teachers" element={<TeacherList />} />
                        <Route path="/schedules" element={<ScheduleManagement />} />
                      </>
                    )}
                    
                    {/* Teacher Routes */}
                    {(isAdmin || isTeacher) && (
                      <>
                        <Route 
                          path="/my-schedule" 
                          element={<TeacherSchedule teacherId={currentUser.teacherId} />} 
                        />
                        <Route 
                          path="/my-workload" 
                          element={<TeacherWorkload teacherId={currentUser.teacherId} />} 
                        />
                      </>
                    )}
                    
                    <Route path="*" element={<Navigate to="/" replace />} />
                  </Routes>
                </main>
              </div>
              <Footer />
            </>
          ) : (
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="*" element={<Navigate to="/login" replace />} />
            </Routes>
          )}
        </div>
      </Router>
    </ThemeProvider>
  );
}

export default App;
