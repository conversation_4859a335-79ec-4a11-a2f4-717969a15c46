{"name": "smart-scheduler-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.0", "@mui/x-data-grid": "^6.4.0", "@mui/x-date-pickers": "^6.4.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "date-fns": "^2.30.0", "formik": "^2.2.9", "jwt-decode": "^3.1.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.11.1", "react-scripts": "5.0.1", "recharts": "^2.6.2", "web-vitals": "^2.1.4", "yup": "^1.1.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8080/api"}