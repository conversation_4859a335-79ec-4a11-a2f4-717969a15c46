# Smart Scheduler - Educational Scheduling System

Smart Scheduler là hệ thống quản lý lịch giảng dạy cho các trường đại học và cao đẳng, giúp tự động hóa việc sắp xếp lịch học và theo dõi khối lượng công việc của giảng viên.

## Tính năng chính

### Quản lý dữ liệu đầu vào
- ✅ Quản lý thông tin khoa, ng<PERSON><PERSON>, lớp học
- ✅ Quản lý thông tin môn học và bài học cụ thể
- ✅ Quản lý thông tin giảng viên theo khoa
- ✅ Quản lý thông tin phòng học và cơ sở
- ✅ Quản lý thông tin thời gian (ti<PERSON><PERSON> họ<PERSON>, thứ, tuần học)

### Quản lý lịch giảng
- ✅ <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, x<PERSON><PERSON> lịch giảng dạy
- ✅ <PERSON>ểm tra xung đột lịch (g<PERSON><PERSON><PERSON> vi<PERSON>, <PERSON>h<PERSON><PERSON> họ<PERSON>, lớ<PERSON> họ<PERSON>)
- ✅ Phân biệt hình thức học: Lý thuyết (LT) và Thực hành (TH)
- ✅ Quản lý nhóm thực hành
- ✅ Tính toán hệ số giờ giảng

### Báo cáo và thống kê
- ✅ Lịch giảng cá nhân của giảng viên (có thể tải về)
- ✅ Thống kê số giờ thực giảng theo hệ số buổi giảng
- ✅ Tổng hợp từ 01/8 năm trước đến 31/7 năm sau
- ✅ Lịch giảng hàng tuần hoàn chỉnh
- ✅ Báo cáo tổng hợp với biểu đồ trực quan

## Công nghệ sử dụng

### Backend
- **Spring Boot 3.2.0** - Framework chính
- **Spring Security** - Bảo mật và xác thực
- **Spring Data JPA** - Quản lý cơ sở dữ liệu
- **MySQL** - Cơ sở dữ liệu
- **JWT** - JSON Web Tokens cho xác thực
- **Maven** - Quản lý dependencies

### Frontend
- **React 18** - Framework frontend
- **Material-UI (MUI)** - Component library
- **Axios** - HTTP client
- **React Router** - Routing
- **Recharts** - Biểu đồ và thống kê

## Yêu cầu hệ thống

- **Java 17** hoặc cao hơn
- **Node.js 16** hoặc cao hơn
- **MySQL 8.0** hoặc cao hơn
- **Maven 3.6** hoặc cao hơn

## Cài đặt và chạy

### 1. Chuẩn bị cơ sở dữ liệu

```sql
-- Tạo database
CREATE DATABASE smartscheduler;

-- Tạo user (tùy chọn)
CREATE USER 'smartscheduler'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON smartscheduler.* TO 'smartscheduler'@'localhost';
FLUSH PRIVILEGES;
```

### 2. Cấu hình Backend

1. Clone repository và di chuyển vào thư mục project:
```bash
cd SmartScheduler
```

2. Cập nhật cấu hình database trong `src/main/resources/application.properties`:
```properties
spring.datasource.url=******************************************
spring.datasource.username=root
spring.datasource.password=your_password
```

3. Chạy backend:
```bash
mvn clean install
mvn spring-boot:run
```

Backend sẽ chạy trên: http://localhost:8080

### 3. Cấu hình Frontend

1. Di chuyển vào thư mục frontend:
```bash
cd frontend
```

2. Cài đặt dependencies:
```bash
npm install
```

3. Chạy frontend:
```bash
npm start
```

Frontend sẽ chạy trên: http://localhost:3000

## Tài khoản mặc định

Hệ thống sẽ tự động tạo tài khoản admin khi khởi động lần đầu:

- **Username**: `admin`
- **Password**: `admin123`
- **Role**: Administrator

## Cấu trúc project

```
SmartScheduler/
├── src/main/java/com/smartscheduler/
│   ├── config/          # Cấu hình Spring
│   ├── controller/      # REST Controllers
│   ├── dto/            # Data Transfer Objects
│   ├── model/          # JPA Entities
│   ├── repository/     # JPA Repositories
│   ├── security/       # JWT Security
│   ├── service/        # Business Logic
│   └── util/           # Utilities
├── src/main/resources/
│   └── application.properties
├── frontend/
│   ├── public/
│   └── src/
│       ├── components/ # React Components
│       ├── pages/      # Page Components
│       ├── services/   # API Services
│       └── utils/      # Utilities
└── pom.xml
```

## API Documentation

Khi backend đang chạy, bạn có thể truy cập API documentation tại:
- Swagger UI: http://localhost:8080/swagger-ui.html

## Đóng góp

1. Fork project
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## License

Distributed under the MIT License. See `LICENSE` for more information.

## Liên hệ

Project Link: [https://github.com/yourusername/smart-scheduler](https://github.com/yourusername/smart-scheduler)
