#!/bin/bash

echo "Starting Smart Scheduler Development Environment..."

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "Port $1 is already in use"
        return 1
    else
        return 0
    fi
}

# Check if required ports are available
if ! check_port 8080; then
    echo "Backend port 8080 is already in use. Please stop the service using this port."
    exit 1
fi

if ! check_port 3000; then
    echo "Frontend port 3000 is already in use. Please stop the service using this port."
    exit 1
fi

echo ""
echo "Starting Backend (Spring Boot)..."
# Start backend in background
mvn spring-boot:run -Dspring-boot.run.profiles=dev &
BACKEND_PID=$!

echo "Backend started with PID: $BACKEND_PID"

echo ""
echo "Waiting for backend to start..."
sleep 15

echo ""
echo "Starting Frontend (React)..."
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "Installing frontend dependencies..."
    npm install
fi

# Start frontend
npm start &
FRONTEND_PID=$!

echo "Frontend started with PID: $FRONTEND_PID"

echo ""
echo "Smart Scheduler is running!"
echo "Backend: http://localhost:8080"
echo "Frontend: http://localhost:3000"
echo ""
echo "Press Ctrl+C to stop all services..."

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "Stopping services..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "Services stopped."
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop
wait
