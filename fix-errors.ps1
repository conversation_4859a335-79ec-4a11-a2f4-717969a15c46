# PowerShell script to fix compilation errors

Write-Host "Fixing Smart Scheduler compilation errors..." -ForegroundColor Green

# Set Java 17 environment
$env:JAVA_HOME = "C:\Program Files\Java\jdk-17"
$env:PATH = "$env:JAVA_HOME\bin;$env:PATH"

Write-Host "Java environment set to Java 17" -ForegroundColor Yellow

# Function to replace text in file
function Replace-InFile {
    param(
        [string]$FilePath,
        [string]$OldText,
        [string]$NewText
    )
    
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw
        $content = $content -replace [regex]::Escape($OldText), $NewText
        Set-Content $FilePath $content -NoNewline
        Write-Host "Fixed: $FilePath" -ForegroundColor Cyan
    }
}

Write-Host "Applying fixes..." -ForegroundColor Yellow

# Fix 1: ResponseEntity type inference issues in controllers
$controllers = @(
    "src/main/java/com/smartscheduler/controller/CampusController.java",
    "src/main/java/com/smartscheduler/controller/DepartmentController.java", 
    "src/main/java/com/smartscheduler/controller/TeacherController.java",
    "src/main/java/com/smartscheduler/controller/ClassController.java",
    "src/main/java/com/smartscheduler/controller/RoomController.java",
    "src/main/java/com/smartscheduler/controller/MajorController.java",
    "src/main/java/com/smartscheduler/controller/CourseController.java",
    "src/main/java/com/smartscheduler/controller/ScheduleController.java",
    "src/main/java/com/smartscheduler/controller/TeacherWorkloadController.java"
)

foreach ($controller in $controllers) {
    Replace-InFile $controller ".map(.*? -> new ResponseEntity<>(.*?, HttpStatus.OK))" ".map(entity -> ResponseEntity.ok(entity))"
    Replace-InFile $controller ".orElse(new ResponseEntity<>(new MessageResponse(.*?), HttpStatus.NOT_FOUND))" ".orElse(ResponseEntity.notFound().build())"
}

Write-Host "Building project..." -ForegroundColor Yellow
mvn clean compile

Write-Host "Fix script completed!" -ForegroundColor Green
