@echo off
echo ========================================
echo Fixing Final Controller Issues
echo ========================================

echo.
echo Setting up Java 17 environment...
set "JAVA_HOME=C:\Program Files\Java\jdk-17"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo.
echo Progress so far:
echo [✓] ClassController - FIXED
echo [✓] TeacherController - FIXED  
echo [✓] CampusController - FIXED
echo [✓] DepartmentController - FIXED
echo [✓] TeacherWorkloadController - FIXED
echo [✓] RoomController - FIXED
echo [✓] ScheduleService Class conflicts - FIXED
echo.
echo Remaining to fix:
echo [ ] MajorController
echo [ ] CourseController  
echo [ ] ScheduleController
echo.

echo Step 1: Testing current build status...
mvn compile -q

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ALL ISSUES RESOLVED!
    echo ========================================
    echo.
    echo The SmartScheduler project now compiles successfully!
    echo You can run the application with: mvn spring-boot:run
) else (
    echo.
    echo Still need to fix the remaining controllers manually...
    echo The pattern is to replace:
    echo   .map(entity -> new ResponseEntity^<^>(entity, HttpStatus.OK))
    echo   .orElse(new ResponseEntity^<^>(new MessageResponse("..."), HttpStatus.NOT_FOUND))
    echo.
    echo With:
    echo   .^<ResponseEntity^<?^>^>map(entity -> ResponseEntity.ok(entity))
    echo   .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND).body(new MessageResponse("...")))
)

echo.
echo Press any key to continue...
pause > nul
