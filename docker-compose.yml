version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: smartscheduler-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: smartscheduler
      MYSQL_USER: smartscheduler
      MYSQL_PASSWORD: smartscheduler123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: smartscheduler-phpmyadmin
    restart: always
    ports:
      - "8081:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root123
    depends_on:
      - mysql

volumes:
  mysql_data:
