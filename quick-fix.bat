@echo off
echo Quick Fix for Smart Scheduler compilation errors...

echo Setting Java 17 environment...
set JAVA_HOME=C:\Program Files\Java\jdk-17
set PATH=%JAVA_HOME%\bin;%PATH%

echo.
echo Applying quick fixes...

echo 1. Fixing ResponseEntity type inference in controllers...
powershell -Command "(Get-Content 'src\main\java\com\smartscheduler\controller\DepartmentController.java') -replace '.map\(department -> new ResponseEntity<>\(department, HttpStatus.OK\)\)', '.map(department -> ResponseEntity.ok(department))' -replace '.orElse\(new ResponseEntity<>\(new MessageResponse\(\"Department not found with id: \" \+ id\), HttpStatus.NOT_FOUND\)\)', '.orElse(ResponseEntity.notFound().build())' | Set-Content 'src\main\java\com\smartscheduler\controller\DepartmentController.java'"

echo 2. Fixing Class name conflicts in ScheduleService...
powershell -Command "(Get-Content 'src\main\java\com\smartscheduler\service\ScheduleService.java') -replace 'Class classEntity', 'com.smartscheduler.model.Class classEntity' | Set-Content 'src\main\java\com\smartscheduler\service\ScheduleService.java'"

echo.
echo Testing build...
mvn clean compile -DskipTests

echo.
echo Quick fix completed!
pause
