# Dữ liệu mẫu SmartScheduler

## Tổng quan
File `sample-data.sql` chứa dữ liệu mẫu đầy đủ cho hệ thống SmartScheduler, bao gồm các thông tin về khoa, ng<PERSON><PERSON>, g<PERSON><PERSON><PERSON> vi<PERSON>, m<PERSON><PERSON> h<PERSON>, <PERSON><PERSON><PERSON>ọ<PERSON>, <PERSON>h<PERSON><PERSON> học và lịch học.

## <PERSON><PERSON>ch sử dụng

### 1. Sử dụng với MySQL Command Line
```bash
mysql -u root -p smartscheduler < sample-data.sql
```

### 2. Sử dụng với phpMyAdmin
1. Mở phpMyAdmin (http://localhost:8081)
2. Chọn database `smartscheduler`
3. Vào tab "Import"
4. Chọn file `sample-data.sql`
5. Click "Go"

### 3. Sử dụng với Docker
```bash
# Copy file vào container
docker cp sample-data.sql smartscheduler-mysql:/tmp/

# Thực thi trong container
docker exec -i smartscheduler-mysql mysql -u root -proot123 smartscheduler < /tmp/sample-data.sql
```

## Dữ liệu được tạo

### 🏢 Departments (Khoa) - 5 records
- Khoa Công nghệ Thông tin (CNTT)
- Khoa Kinh tế (KT)
- Khoa Ngoại ngữ (NN)
- Khoa Kỹ thuật (KTH)
- Khoa Khoa học Tự nhiên (KHTN)

### 🎓 Majors (Ngành học) - 10 records
- Công nghệ Phần mềm (CNPM)
- Hệ thống Thông tin (HTTT)
- An toàn Thông tin (ATTT)
- Quản trị Kinh doanh (QTKD)
- Kế toán (KT)
- Tiếng Anh (TA)
- Tiếng Nhật (TN)
- Kỹ thuật Điện (KTD)
- Kỹ thuật Cơ khí (KTCK)
- Toán học (TH)

### 👨‍🏫 Teachers (Giảng viên) - 10 records
- GV001: Nguyễn Văn An (CNTT)
- GV002: Trần Thị Bình (CNTT)
- GV003: Lê Hoàng Cường (CNTT)
- GV004: Phạm Thị Dung (Kinh tế)
- GV005: Hoàng Văn Em (Kinh tế)
- GV006: Vũ Thị Phương (Ngoại ngữ)
- GV007: Đặng Minh Giang (Ngoại ngữ)
- GV008: Bùi Văn Hải (Kỹ thuật)
- GV009: Ngô Thị Lan (Kỹ thuật)
- GV010: Đinh Văn Khoa (KHTN)

### 📚 Courses (Môn học) - 15 records
**Công nghệ Thông tin:**
- Lập trình Java (JAVA101) - Theory
- Thực hành Java (JAVA101P) - Practice
- Cơ sở dữ liệu (DB101) - Theory
- Thực hành Cơ sở dữ liệu (DB101P) - Practice
- Kỹ thuật phần mềm (SE101) - Theory

**Kinh tế:**
- Quản trị học (MGT101) - Theory
- Kế toán tài chính (ACC101) - Theory

**Ngoại ngữ:**
- Tiếng Anh giao tiếp (ENG101) - Practice
- Tiếng Nhật cơ bản (JPN101) - Theory

**Kỹ thuật:**
- Mạch điện tử (ELE101) - Theory
- Thực hành Mạch điện tử (ELE101P) - Practice
- Cơ học kỹ thuật (MECH101) - Theory

**Khoa học Tự nhiên:**
- Giải tích 1 (MATH101) - Theory
- Đại số tuyến tính (MATH102) - Theory
- Xác suất thống kê (STAT101) - Theory

### 🎒 Classes (Lớp học) - 12 records
- CNPM2024A, CNPM2024B (Công nghệ Phần mềm)
- HTTT2024A (Hệ thống Thông tin)
- ATTT2024A (An toàn Thông tin)
- QTKD2024A, QTKD2024B (Quản trị Kinh doanh)
- KT2024A (Kế toán)
- TA2024A (Tiếng Anh)
- TN2024A (Tiếng Nhật)
- KTD2024A (Kỹ thuật Điện)
- KTCK2024A (Kỹ thuật Cơ khí)
- TH2024A (Toán học)

### 🏫 Rooms (Phòng học) - 10 records
**Cơ sở 1:**
- A101, A102, A103 (Theory rooms)
- B101, B102, B103 (Practice rooms)

**Cơ sở 2:**
- C101, C102 (Theory rooms)
- D101, D102 (Practice rooms)

### 📅 Schedules (Lịch học) - 25 records
Lịch học mẫu cho tuần đầu tiên (tuần 1) của học kỳ 1 năm học 2024-2025, bao gồm:
- Thứ 2: 5 buổi học
- Thứ 3: 5 buổi học
- Thứ 4: 5 buổi học
- Thứ 5: 5 buổi học
- Thứ 6: 5 buổi học

### 👤 User Accounts (Tài khoản)
**Admin Account:**
- Username: `admin`
- Password: `admin123`
- Role: ADMIN

**Teacher Accounts:**
- Username: `gv001` - `gv005`
- Password: `password123` (cho tất cả)
- Role: TEACHER

### ⏰ Time Slots (Khung giờ học)
- Tiết 1-2: 07:00-08:30 (MORNING)
- Tiết 3-4: 08:45-10:15 (MORNING)
- Tiết 5-6: 10:30-12:00 (MORNING)
- Tiết 7-8: 13:00-14:30 (AFTERNOON)
- Tiết 9-10: 14:45-16:15 (AFTERNOON)
- Tiết 11-12: 16:30-18:00 (AFTERNOON)
- Tiết 13-14: 18:15-19:45 (EVENING)

### 📊 Academic Weeks (Tuần học)
15 tuần học cho học kỳ 1 năm học 2024-2025 (từ 02/09/2024 đến 15/12/2024)

## Lưu ý quan trọng

1. **Backup dữ liệu hiện tại** trước khi import dữ liệu mẫu
2. File sử dụng `INSERT IGNORE` để tránh lỗi duplicate key
3. Mật khẩu đã được mã hóa bằng BCrypt
4. Dữ liệu được thiết kế cho môi trường development/testing
5. Có thể cần điều chỉnh ID sequence sau khi import

## Kiểm tra dữ liệu sau khi import

```sql
-- Kiểm tra số lượng records
SELECT 'Departments' as table_name, COUNT(*) as count FROM departments
UNION ALL
SELECT 'Majors', COUNT(*) FROM majors
UNION ALL
SELECT 'Teachers', COUNT(*) FROM teachers
UNION ALL
SELECT 'Courses', COUNT(*) FROM courses
UNION ALL
SELECT 'Classes', COUNT(*) FROM classes
UNION ALL
SELECT 'Rooms', COUNT(*) FROM rooms
UNION ALL
SELECT 'Schedules', COUNT(*) FROM schedules
UNION ALL
SELECT 'Users', COUNT(*) FROM users;
```

## Troubleshooting

### Lỗi Foreign Key
Nếu gặp lỗi foreign key, hãy chắc chắn rằng:
1. Database đã có dữ liệu cơ bản từ `init.sql`
2. Thứ tự import đúng (dependencies trước)

### Lỗi Duplicate Entry
File sử dụng `INSERT IGNORE` nên sẽ bỏ qua các record trùng lặp.

### Reset dữ liệu
Để reset hoàn toàn:
```sql
SET FOREIGN_KEY_CHECKS = 0;
-- Xóa dữ liệu các bảng
TRUNCATE TABLE schedules;
TRUNCATE TABLE teacher_workloads;
-- ... (các bảng khác)
SET FOREIGN_KEY_CHECKS = 1;
```
