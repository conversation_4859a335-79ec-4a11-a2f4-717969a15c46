@echo off
echo Starting Smart Scheduler Development Environment...

echo.
echo Starting Backend (Spring Boot)...
start "Backend" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=dev"

echo.
echo Waiting for backend to start...
timeout /t 10 /nobreak > nul

echo.
echo Starting Frontend (React)...
cd frontend
start "Frontend" cmd /k "npm start"

echo.
echo Smart Scheduler is starting up...
echo Backend: http://localhost:8080
echo Frontend: http://localhost:3000
echo.
echo Press any key to exit...
pause > nul
