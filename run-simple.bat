@echo off
echo Starting Smart Scheduler (Simple Version)...

echo Setting Java 17 environment...
set JAVA_HOME=C:\Program Files\Java\jdk-17
set PATH=%JAVA_HOME%\bin;%PATH%

echo.
echo Creating minimal working version...

echo 1. Temporarily disabling problematic controllers...
if not exist "backup" mkdir backup
copy "src\main\java\com\smartscheduler\controller\*.java" "backup\" >nul 2>&1

echo 2. Running with only AuthController...
mvn spring-boot:run -DskipTests -Dspring.profiles.active=dev

pause
