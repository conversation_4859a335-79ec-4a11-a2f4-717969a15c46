@echo off
echo Fixing Smart Scheduler and running...

echo Setting Java 17 environment...
set JAVA_HOME=C:\Program Files\Java\jdk-17
set PATH=%JAVA_HOME%\bin;%PATH%

echo.
echo Backing up original files...
if not exist "backup" mkdir backup
copy "src\main\java\com\smartscheduler\controller\DepartmentController.java" "backup\" >nul 2>&1

echo.
echo Replacing with fixed version...
copy "src\main\java\com\smartscheduler\controller\DepartmentControllerFixed.java" "src\main\java\com\smartscheduler\controller\DepartmentController.java"

echo.
echo Temporarily disabling problematic controllers...
ren "src\main\java\com\smartscheduler\controller\TeacherController.java" "TeacherController.java.bak"
ren "src\main\java\com\smartscheduler\controller\ClassController.java" "ClassController.java.bak"
ren "src\main\java\com\smartscheduler\controller\ScheduleController.java" "ScheduleController.java.bak"
ren "src\main\java\com\smartscheduler\controller\TeacherWorkloadController.java" "TeacherWorkloadController.java.bak"
ren "src\main\java\com\smartscheduler\controller\RoomController.java" "RoomController.java.bak"
ren "src\main\java\com\smartscheduler\controller\MajorController.java" "MajorController.java.bak"
ren "src\main\java\com\smartscheduler\controller\CourseController.java" "CourseController.java.bak"
ren "src\main\java\com\smartscheduler\controller\CampusController.java" "CampusController.java.bak"

echo.
echo Building minimal version...
mvn clean compile -DskipTests

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful! Starting application...
    mvn spring-boot:run -DskipTests
) else (
    echo.
    echo Build failed. Check errors above.
    pause
)

echo.
echo Restoring original files...
ren "src\main\java\com\smartscheduler\controller\TeacherController.java.bak" "TeacherController.java"
ren "src\main\java\com\smartscheduler\controller\ClassController.java.bak" "ClassController.java"
ren "src\main\java\com\smartscheduler\controller\ScheduleController.java.bak" "ScheduleController.java"
ren "src\main\java\com\smartscheduler\controller\TeacherWorkloadController.java.bak" "TeacherWorkloadController.java"
ren "src\main\java\com\smartscheduler\controller\RoomController.java.bak" "RoomController.java"
ren "src\main\java\com\smartscheduler\controller\MajorController.java.bak" "MajorController.java"
ren "src\main\java\com\smartscheduler\controller\CourseController.java.bak" "CourseController.java"
ren "src\main\java\com\smartscheduler\controller\CampusController.java.bak" "CampusController.java"

pause
