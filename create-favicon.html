<!DOCTYPE html>
<html>
<head>
    <title>Create Favicon for SmartScheduler</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .container { max-width: 800px; margin: 0 auto; }
        .step { margin: 20px 0; padding: 15px; background: #f5f5f5; border-radius: 5px; }
        button { padding: 10px 20px; background: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #1565c0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>SmartScheduler Favicon Generator</h1>
        
        <div class="step">
            <h3>Step 1: Generate Favicon</h3>
            <p>Click the button below to generate a favicon for SmartScheduler:</p>
            <button onclick="generateFavicon()">Generate Favicon</button>
            <br><br>
            <canvas id="favicon" width="32" height="32"></canvas>
        </div>
        
        <div class="step">
            <h3>Step 2: Download</h3>
            <p>Right-click on the generated favicon and save it as "favicon.ico" in the frontend/public folder.</p>
            <a id="downloadLink" style="display: none;">
                <button>Download favicon.ico</button>
            </a>
        </div>
        
        <div class="step">
            <h3>Step 3: Alternative Solution</h3>
            <p>If the above doesn't work, you can:</p>
            <ol>
                <li>Go to <a href="https://favicon.io/favicon-generator/" target="_blank">favicon.io</a></li>
                <li>Create a favicon with text "SS" (SmartScheduler)</li>
                <li>Download and place it in frontend/public/favicon.ico</li>
            </ol>
        </div>
        
        <div class="step">
            <h3>Step 4: Create Logo Images</h3>
            <p>You also need logo192.png and logo512.png. You can:</p>
            <ol>
                <li>Use the same design but in PNG format</li>
                <li>Create them at <a href="https://www.canva.com" target="_blank">Canva</a> or similar tools</li>
                <li>Use a simple blue square with "SS" text for now</li>
            </ol>
        </div>
    </div>

    <script>
        function generateFavicon() {
            const canvas = document.getElementById('favicon');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 32, 32);
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, 32, 32);
            gradient.addColorStop(0, '#1976d2');
            gradient.addColorStop(1, '#1565c0');
            
            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(16, 16, 15, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw border
            ctx.strokeStyle = '#0d47a1';
            ctx.lineWidth = 1;
            ctx.stroke();
            
            // Draw calendar
            ctx.fillStyle = 'white';
            ctx.fillRect(8, 10, 16, 12);
            
            // Calendar header
            ctx.fillStyle = '#e3f2fd';
            ctx.fillRect(8, 10, 16, 4);
            
            // Calendar grid
            ctx.strokeStyle = '#1976d2';
            ctx.lineWidth = 0.5;
            
            // Vertical lines
            for (let x = 11; x <= 20; x += 3) {
                ctx.beginPath();
                ctx.moveTo(x, 14);
                ctx.lineTo(x, 22);
                ctx.stroke();
            }
            
            // Horizontal lines
            for (let y = 16; y <= 20; y += 2) {
                ctx.beginPath();
                ctx.moveTo(8, y);
                ctx.lineTo(24, y);
                ctx.stroke();
            }
            
            // Calendar rings
            ctx.fillStyle = '#666';
            ctx.fillRect(10, 8, 1, 4);
            ctx.fillRect(21, 8, 1, 4);
            
            // Smart indicator
            ctx.fillStyle = '#4caf50';
            ctx.beginPath();
            ctx.arc(19, 17, 1.5, 0, 2 * Math.PI);
            ctx.fill();
            
            // Create download link
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const link = document.getElementById('downloadLink');
                link.href = url;
                link.download = 'favicon.ico';
                link.style.display = 'inline-block';
            });
        }
    </script>
</body>
</html>
