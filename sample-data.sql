-- Smart Scheduler Sample Data
-- <PERSON><PERSON> liệu mẫu cho hệ thống quản lý lịch học thông minh

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

USE smartscheduler;

-- Clear existing data (optional - uncomment if needed)
-- DELETE FROM schedules;
-- DELETE FROM teacher_workloads;
-- DELETE FROM lessons;
-- DELETE FROM classes;
-- DELETE FROM courses;
-- DELETE FROM teachers;
-- DELETE FROM rooms;
-- DELETE FROM majors;
-- DELETE FROM departments;
-- DELETE FROM users WHERE username != 'admin';

-- Sample Departments (Khoa)
INSERT IGNORE INTO departments (id, name, code, description, created_at, updated_at) VALUES
(1, 'Khoa Công nghệ Thông tin', 'CNTT', 'Khoa đào tạo về công nghệ thông tin và khoa học máy t<PERSON>h', NOW(), NOW()),
(2, '<PERSON><PERSON><PERSON>nh tế', 'KT', '<PERSON><PERSON><PERSON> đào tạo về kinh tế và quản trị kinh doanh', NOW(), NOW()),
(3, 'Khoa Ngoại ngữ', 'NN', 'Khoa đào tạo về ngoại ngữ và văn hóa quốc tế', NOW(), NOW()),
(4, 'Khoa Kỹ thuật', 'KTH', 'Khoa đào tạo về kỹ thuật và công nghệ', NOW(), NOW()),
(5, 'Khoa Khoa học Tự nhiên', 'KHTN', 'Khoa đào tạo về toán học, vật lý, hóa học', NOW(), NOW());

-- Sample Majors (Ngành học)
INSERT IGNORE INTO majors (id, name, code, description, department_id, created_at, updated_at) VALUES
(1, 'Công nghệ Phần mềm', 'CNPM', 'Ngành đào tạo về phát triển phần mềm', 1, NOW(), NOW()),
(2, 'Hệ thống Thông tin', 'HTTT', 'Ngành đào tạo về quản lý hệ thống thông tin', 1, NOW(), NOW()),
(3, 'An toàn Thông tin', 'ATTT', 'Ngành đào tạo về bảo mật và an toàn thông tin', 1, NOW(), NOW()),
(4, 'Quản trị Kinh doanh', 'QTKD', 'Ngành đào tạo về quản lý và điều hành doanh nghiệp', 2, NOW(), NOW()),
(5, 'Kế toán', 'KT', 'Ngành đào tạo về kế toán và tài chính', 2, NOW(), NOW()),
(6, 'Tiếng Anh', 'TA', 'Ngành đào tạo về ngôn ngữ và văn hóa Anh-Mỹ', 3, NOW(), NOW()),
(7, 'Tiếng Nhật', 'TN', 'Ngành đào tạo về ngôn ngữ và văn hóa Nhật Bản', 3, NOW(), NOW()),
(8, 'Kỹ thuật Điện', 'KTD', 'Ngành đào tạo về kỹ thuật điện và điện tử', 4, NOW(), NOW()),
(9, 'Kỹ thuật Cơ khí', 'KTCK', 'Ngành đào tạo về kỹ thuật cơ khí và chế tạo máy', 4, NOW(), NOW()),
(10, 'Toán học', 'TH', 'Ngành đào tạo về toán học ứng dụng', 5, NOW(), NOW());

-- Sample Teachers (Giảng viên)
INSERT IGNORE INTO teachers (id, first_name, last_name, email, phone, teacher_code, department_id, created_at, updated_at) VALUES
(1, 'Nguyễn', 'Văn An', '<EMAIL>', '0901234567', 'GV001', 1, NOW(), NOW()),
(2, 'Trần', 'Thị Bình', '<EMAIL>', '0901234568', 'GV002', 1, NOW(), NOW()),
(3, 'Lê', 'Hoàng Cường', '<EMAIL>', '0901234569', 'GV003', 1, NOW(), NOW()),
(4, 'Phạm', 'Thị Dung', '<EMAIL>', '0901234570', 'GV004', 2, NOW(), NOW()),
(5, 'Hoàng', 'Văn Em', '<EMAIL>', '0901234571', 'GV005', 2, NOW(), NOW()),
(6, 'Vũ', 'Thị Phương', '<EMAIL>', '0901234572', 'GV006', 3, NOW(), NOW()),
(7, 'Đặng', 'Minh Giang', '<EMAIL>', '0901234573', 'GV007', 3, NOW(), NOW()),
(8, 'Bùi', 'Văn Hải', '<EMAIL>', '0901234574', 'GV008', 4, NOW(), NOW()),
(9, 'Ngô', 'Thị Lan', '<EMAIL>', '0901234575', 'GV009', 4, NOW(), NOW()),
(10, 'Đinh', 'Văn Khoa', '<EMAIL>', '0901234576', 'GV010', 5, NOW(), NOW());

-- Sample Courses (Môn học)
INSERT IGNORE INTO courses (id, name, code, description, credits, total_hours, teaching_format, created_at, updated_at) VALUES
(1, 'Lập trình Java', 'JAVA101', 'Môn học cơ bản về lập trình Java', 3, 45, 'THEORY', NOW(), NOW()),
(2, 'Thực hành Java', 'JAVA101P', 'Thực hành lập trình Java', 2, 30, 'PRACTICE', NOW(), NOW()),
(3, 'Cơ sở dữ liệu', 'DB101', 'Môn học về thiết kế và quản lý cơ sở dữ liệu', 3, 45, 'THEORY', NOW(), NOW()),
(4, 'Thực hành Cơ sở dữ liệu', 'DB101P', 'Thực hành thiết kế cơ sở dữ liệu', 2, 30, 'PRACTICE', NOW(), NOW()),
(5, 'Kỹ thuật phần mềm', 'SE101', 'Môn học về quy trình phát triển phần mềm', 3, 45, 'THEORY', NOW(), NOW()),
(6, 'Quản trị học', 'MGT101', 'Môn học cơ bản về quản trị doanh nghiệp', 3, 45, 'THEORY', NOW(), NOW()),
(7, 'Kế toán tài chính', 'ACC101', 'Môn học về kế toán và tài chính doanh nghiệp', 3, 45, 'THEORY', NOW(), NOW()),
(8, 'Tiếng Anh giao tiếp', 'ENG101', 'Môn học về kỹ năng giao tiếp tiếng Anh', 2, 30, 'PRACTICE', NOW(), NOW()),
(9, 'Tiếng Nhật cơ bản', 'JPN101', 'Môn học tiếng Nhật cho người mới bắt đầu', 3, 45, 'THEORY', NOW(), NOW()),
(10, 'Mạch điện tử', 'ELE101', 'Môn học về thiết kế mạch điện tử', 3, 45, 'THEORY', NOW(), NOW()),
(11, 'Thực hành Mạch điện tử', 'ELE101P', 'Thực hành thiết kế mạch điện tử', 2, 30, 'PRACTICE', NOW(), NOW()),
(12, 'Cơ học kỹ thuật', 'MECH101', 'Môn học về cơ học ứng dụng trong kỹ thuật', 3, 45, 'THEORY', NOW(), NOW()),
(13, 'Giải tích 1', 'MATH101', 'Môn học về giải tích toán học', 4, 60, 'THEORY', NOW(), NOW()),
(14, 'Đại số tuyến tính', 'MATH102', 'Môn học về đại số và ma trận', 3, 45, 'THEORY', NOW(), NOW()),
(15, 'Xác suất thống kê', 'STAT101', 'Môn học về xác suất và thống kê', 3, 45, 'THEORY', NOW(), NOW());

-- Sample Classes (Lớp học)
INSERT IGNORE INTO classes (id, name, code, year, semester, student_count, major_id, created_at, updated_at) VALUES
(1, 'CNPM2024A', 'CNPM24A', 2024, 1, 35, 1, NOW(), NOW()),
(2, 'CNPM2024B', 'CNPM24B', 2024, 1, 32, 1, NOW(), NOW()),
(3, 'HTTT2024A', 'HTTT24A', 2024, 1, 30, 2, NOW(), NOW()),
(4, 'ATTT2024A', 'ATTT24A', 2024, 1, 28, 3, NOW(), NOW()),
(5, 'QTKD2024A', 'QTKD24A', 2024, 1, 40, 4, NOW(), NOW()),
(6, 'QTKD2024B', 'QTKD24B', 2024, 1, 38, 4, NOW(), NOW()),
(7, 'KT2024A', 'KT24A', 2024, 1, 35, 5, NOW(), NOW()),
(8, 'TA2024A', 'TA24A', 2024, 1, 25, 6, NOW(), NOW()),
(9, 'TN2024A', 'TN24A', 2024, 1, 20, 7, NOW(), NOW()),
(10, 'KTD2024A', 'KTD24A', 2024, 1, 30, 8, NOW(), NOW()),
(11, 'KTCK2024A', 'KTCK24A', 2024, 1, 28, 9, NOW(), NOW()),
(12, 'TH2024A', 'TH24A', 2024, 1, 25, 10, NOW(), NOW());

-- Sample Rooms (Phòng học)
INSERT IGNORE INTO rooms (id, name, code, capacity, room_type, campus_id, created_at, updated_at) VALUES
(1, 'Phòng A101', 'A101', 50, 'THEORY', 1, NOW(), NOW()),
(2, 'Phòng A102', 'A102', 45, 'THEORY', 1, NOW(), NOW()),
(3, 'Phòng A103', 'A103', 40, 'THEORY', 1, NOW(), NOW()),
(4, 'Phòng B101', 'B101', 30, 'PRACTICE', 1, NOW(), NOW()),
(5, 'Phòng B102', 'B102', 30, 'PRACTICE', 1, NOW(), NOW()),
(6, 'Phòng B103', 'B103', 25, 'PRACTICE', 1, NOW(), NOW()),
(7, 'Phòng C101', 'C101', 60, 'THEORY', 2, NOW(), NOW()),
(8, 'Phòng C102', 'C102', 55, 'THEORY', 2, NOW(), NOW()),
(9, 'Phòng D101', 'D101', 35, 'PRACTICE', 2, NOW(), NOW()),
(10, 'Phòng D102', 'D102', 35, 'PRACTICE', 2, NOW(), NOW());

-- Sample Lessons (Bài học)
INSERT IGNORE INTO lessons (id, name, sequence_number, hours, coefficient, course_id, created_at, updated_at) VALUES
(1, 'Giới thiệu Java', 1, 3, 1.0, 1, NOW(), NOW()),
(2, 'Cú pháp cơ bản Java', 2, 3, 1.0, 1, NOW(), NOW()),
(3, 'Lập trình hướng đối tượng', 3, 3, 1.2, 1, NOW(), NOW()),
(4, 'Thực hành cơ bản', 1, 2, 1.0, 2, NOW(), NOW()),
(5, 'Thực hành nâng cao', 2, 2, 1.2, 2, NOW(), NOW()),
(6, 'Mô hình dữ liệu', 1, 3, 1.0, 3, NOW(), NOW()),
(7, 'SQL cơ bản', 2, 3, 1.0, 3, NOW(), NOW()),
(8, 'Thiết kế CSDL', 3, 3, 1.2, 3, NOW(), NOW());

-- Sample Users for Teachers (Tài khoản cho giảng viên)
INSERT IGNORE INTO users (id, username, password, email, teacher_id, is_account_non_expired, is_account_non_locked, is_credentials_non_expired, is_enabled, created_at, updated_at) VALUES
(2, 'gv001', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iDXMcVulpZbcAB0aOKjbwxHeqWQi', '<EMAIL>', 1, 1, 1, 1, 1, NOW(), NOW()),
(3, 'gv002', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iDXMcVulpZbcAB0aOKjbwxHeqWQi', '<EMAIL>', 2, 1, 1, 1, 1, NOW(), NOW()),
(4, 'gv003', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iDXMcVulpZbcAB0aOKjbwxHeqWQi', '<EMAIL>', 3, 1, 1, 1, 1, NOW(), NOW()),
(5, 'gv004', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iDXMcVulpZbcAB0aOKjbwxHeqWQi', '<EMAIL>', 4, 1, 1, 1, 1, NOW(), NOW()),
(6, 'gv005', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iDXMcVulpZbcAB0aOKjbwxHeqWQi', '<EMAIL>', 5, 1, 1, 1, 1, NOW(), NOW());

-- Assign TEACHER role to teacher users
INSERT IGNORE INTO user_roles (user_id, role_id) VALUES
(2, 2), (3, 2), (4, 2), (5, 2), (6, 2);

-- Sample Schedules (Lịch học) - Tuần 1
INSERT IGNORE INTO schedules (id, teacher_id, course_id, lesson_id, class_id, room_id, time_slot_id, week_day_id, academic_week_id, practice_group, hours, coefficient, teaching_format, notes, created_at, updated_at) VALUES
-- Thứ 2
(1, 1, 1, 1, 1, 1, 1, 1, 1, NULL, 3, 1.0, 'THEORY', 'Buổi học đầu tiên', NOW(), NOW()),
(2, 2, 3, 6, 3, 2, 2, 1, 1, NULL, 3, 1.0, 'THEORY', 'Giới thiệu CSDL', NOW(), NOW()),
(3, 4, 6, NULL, 5, 3, 3, 1, 1, NULL, 3, 1.0, 'THEORY', 'Quản trị cơ bản', NOW(), NOW()),
(4, 1, 2, 4, 1, 4, 4, 1, 1, 1, 2, 1.0, 'PRACTICE', 'Thực hành Java nhóm 1', NOW(), NOW()),
(5, 3, 5, NULL, 2, 1, 5, 1, 1, NULL, 3, 1.2, 'THEORY', 'Kỹ thuật phần mềm', NOW(), NOW()),

-- Thứ 3
(6, 2, 4, NULL, 3, 5, 1, 2, 1, 1, 2, 1.0, 'PRACTICE', 'Thực hành CSDL nhóm 1', NOW(), NOW()),
(7, 5, 7, NULL, 7, 2, 2, 2, 1, NULL, 3, 1.0, 'THEORY', 'Kế toán tài chính', NOW(), NOW()),
(8, 6, 8, NULL, 8, 3, 3, 2, 1, NULL, 2, 1.0, 'PRACTICE', 'Tiếng Anh giao tiếp', NOW(), NOW()),
(9, 1, 1, 2, 2, 1, 4, 2, 1, NULL, 3, 1.0, 'THEORY', 'Cú pháp Java', NOW(), NOW()),
(10, 7, 9, NULL, 9, 6, 5, 2, 1, NULL, 3, 1.0, 'THEORY', 'Tiếng Nhật cơ bản', NOW(), NOW()),

-- Thứ 4
(11, 8, 10, NULL, 10, 1, 1, 3, 1, NULL, 3, 1.0, 'THEORY', 'Mạch điện tử', NOW(), NOW()),
(12, 9, 12, NULL, 11, 2, 2, 3, 1, NULL, 3, 1.0, 'THEORY', 'Cơ học kỹ thuật', NOW(), NOW()),
(13, 10, 13, NULL, 12, 3, 3, 3, 1, NULL, 4, 1.0, 'THEORY', 'Giải tích 1', NOW(), NOW()),
(14, 8, 11, NULL, 10, 9, 4, 3, 1, 1, 2, 1.2, 'PRACTICE', 'Thực hành mạch điện tử', NOW(), NOW()),
(15, 3, 1, 3, 4, 1, 5, 3, 1, NULL, 3, 1.2, 'THEORY', 'OOP trong Java', NOW(), NOW()),

-- Thứ 5
(16, 1, 2, 5, 1, 4, 1, 4, 1, 2, 2, 1.2, 'PRACTICE', 'Thực hành Java nhóm 2', NOW(), NOW()),
(17, 4, 6, NULL, 6, 2, 2, 4, 1, NULL, 3, 1.0, 'THEORY', 'Quản trị học', NOW(), NOW()),
(18, 2, 3, 7, 3, 1, 3, 4, 1, NULL, 3, 1.0, 'THEORY', 'SQL cơ bản', NOW(), NOW()),
(19, 6, 8, NULL, 8, 6, 4, 4, 1, NULL, 2, 1.0, 'PRACTICE', 'Giao tiếp nâng cao', NOW(), NOW()),
(20, 10, 14, NULL, 12, 3, 5, 4, 1, NULL, 3, 1.0, 'THEORY', 'Đại số tuyến tính', NOW(), NOW()),

-- Thứ 6
(21, 5, 7, NULL, 7, 2, 1, 5, 1, NULL, 3, 1.0, 'THEORY', 'Kế toán nâng cao', NOW(), NOW()),
(22, 7, 9, NULL, 9, 3, 2, 5, 1, NULL, 3, 1.0, 'THEORY', 'Ngữ pháp Nhật', NOW(), NOW()),
(23, 2, 4, NULL, 3, 5, 3, 5, 1, 2, 2, 1.0, 'PRACTICE', 'Thực hành CSDL nhóm 2', NOW(), NOW()),
(24, 9, 12, NULL, 11, 1, 4, 5, 1, NULL, 3, 1.0, 'THEORY', 'Cơ học ứng dụng', NOW(), NOW()),
(25, 10, 15, NULL, 12, 3, 5, 5, 1, NULL, 3, 1.0, 'THEORY', 'Xác suất thống kê', NOW(), NOW());

-- Sample Teacher Workloads (Khối lượng công việc giảng viên)
INSERT IGNORE INTO teacher_workloads (id, teacher_id, course_id, class_id, academic_year, semester, teaching_date, hours, coefficient, weighted_hours, teaching_format, created_at, updated_at) VALUES
(1, 1, 1, 1, '2024-2025', 1, '2024-09-02', 3, 1.0, 3.0, 'THEORY', NOW(), NOW()),
(2, 1, 2, 1, '2024-2025', 1, '2024-09-02', 2, 1.0, 2.0, 'PRACTICE', NOW(), NOW()),
(3, 2, 3, 3, '2024-2025', 1, '2024-09-02', 3, 1.0, 3.0, 'THEORY', NOW(), NOW()),
(4, 2, 4, 3, '2024-2025', 1, '2024-09-03', 2, 1.0, 2.0, 'PRACTICE', NOW(), NOW()),
(5, 3, 5, 2, '2024-2025', 1, '2024-09-02', 3, 1.2, 3.6, 'THEORY', NOW(), NOW()),
(6, 4, 6, 5, '2024-2025', 1, '2024-09-02', 3, 1.0, 3.0, 'THEORY', NOW(), NOW()),
(7, 4, 6, 6, '2024-2025', 1, '2024-09-05', 3, 1.0, 3.0, 'THEORY', NOW(), NOW()),
(8, 5, 7, 7, '2024-2025', 1, '2024-09-03', 3, 1.0, 3.0, 'THEORY', NOW(), NOW()),
(9, 6, 8, 8, '2024-2025', 1, '2024-09-03', 2, 1.0, 2.0, 'PRACTICE', NOW(), NOW()),
(10, 7, 9, 9, '2024-2025', 1, '2024-09-03', 3, 1.0, 3.0, 'THEORY', NOW(), NOW());

-- Additional Academic Weeks for full semester
INSERT IGNORE INTO academic_weeks (id, week_number, start_date, end_date, academic_year, semester, created_at, updated_at) VALUES
(6, 6, '2024-10-07', '2024-10-13', '2024-2025', 1, NOW(), NOW()),
(7, 7, '2024-10-14', '2024-10-20', '2024-2025', 1, NOW(), NOW()),
(8, 8, '2024-10-21', '2024-10-27', '2024-2025', 1, NOW(), NOW()),
(9, 9, '2024-10-28', '2024-11-03', '2024-2025', 1, NOW(), NOW()),
(10, 10, '2024-11-04', '2024-11-10', '2024-2025', 1, NOW(), NOW()),
(11, 11, '2024-11-11', '2024-11-17', '2024-2025', 1, NOW(), NOW()),
(12, 12, '2024-11-18', '2024-11-24', '2024-2025', 1, NOW(), NOW()),
(13, 13, '2024-11-25', '2024-12-01', '2024-2025', 1, NOW(), NOW()),
(14, 14, '2024-12-02', '2024-12-08', '2024-2025', 1, NOW(), NOW()),
(15, 15, '2024-12-09', '2024-12-15', '2024-2025', 1, NOW(), NOW());

SET FOREIGN_KEY_CHECKS = 1;

-- Summary of sample data created:
-- ✓ 5 Departments (Khoa)
-- ✓ 10 Majors (Ngành học)
-- ✓ 10 Teachers (Giảng viên)
-- ✓ 15 Courses (Môn học)
-- ✓ 12 Classes (Lớp học)
-- ✓ 10 Rooms (Phòng học)
-- ✓ 8 Lessons (Bài học)
-- ✓ 25 Schedules (Lịch học cho tuần 1)
-- ✓ 10 Teacher Workloads (Khối lượng công việc)
-- ✓ 5 Teacher User Accounts (Tài khoản giảng viên)
-- ✓ 15 Academic Weeks (Tuần học)

-- Default passwords for all teacher accounts: "password123"
-- Admin account: username="admin", password="admin123"
