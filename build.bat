@echo off
echo Setting up Java 17 environment...

set "JAVA_HOME=C:\Program Files\Java\jdk-17"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo JAVA_HOME: %JAVA_HOME%
echo Java version:
java -version

echo.
echo Maven version:
mvn -version

echo.
echo Building Smart Scheduler...
mvn clean compile

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build completed successfully!
) else (
    echo.
    echo Build failed! Check errors above.
)
pause
