-- Smart Scheduler Database Initialization Script

-- Set character set
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- Create database if not exists (already handled by docker-compose)
-- CREATE DATABASE IF NOT EXISTS smartscheduler CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE smartscheduler;

-- Grant additional privileges
GRANT ALL PRIVILEGES ON smartscheduler.* TO 'smartscheduler'@'%';
FLUSH PRIVILEGES;

-- Insert sample data for development (optional)
-- This will be handled by Spring Boot's DatabaseInitializer

-- Sample campuses
INSERT IGNORE INTO campuses (id, name, code, address, created_at, updated_at) VALUES
(1, 'Cơ sở 1', 'CS1', '123 Đường ABC, Quận 1, TP.HCM', NOW(), NOW()),
(2, 'C<PERSON> sở 2', 'CS2', '456 Đường XYZ, Quận 2, TP.HCM', NOW(), NOW());

-- <PERSON><PERSON> time slots
INSERT IGNORE INTO time_slots (id, name, start_time, end_time, session, created_at, updated_at) VALUES
(1, 'Tiết 1-2', '07:00:00', '08:30:00', 'MORNING', NOW(), NOW()),
(2, 'Tiết 3-4', '08:45:00', '10:15:00', 'MORNING', NOW(), NOW()),
(3, 'Tiết 5-6', '10:30:00', '12:00:00', 'MORNING', NOW(), NOW()),
(4, 'Tiết 7-8', '13:00:00', '14:30:00', 'AFTERNOON', NOW(), NOW()),
(5, 'Tiết 9-10', '14:45:00', '16:15:00', 'AFTERNOON', NOW(), NOW()),
(6, 'Tiết 11-12', '16:30:00', '18:00:00', 'AFTERNOON', NOW(), NOW()),
(7, 'Tiết 13-14', '18:15:00', '19:45:00', 'EVENING', NOW(), NOW());

-- Sample week days
INSERT IGNORE INTO week_days (id, name, day_order, created_at, updated_at) VALUES
(1, 'Thứ 2', 1, NOW(), NOW()),
(2, 'Thứ 3', 2, NOW(), NOW()),
(3, 'Thứ 4', 3, NOW(), NOW()),
(4, 'Thứ 5', 4, NOW(), NOW()),
(5, 'Thứ 6', 5, NOW(), NOW()),
(6, 'Thứ 7', 6, NOW(), NOW()),
(7, 'Chủ nhật', 7, NOW(), NOW());

-- Sample academic weeks
INSERT IGNORE INTO academic_weeks (id, week_number, start_date, end_date, academic_year, semester, created_at, updated_at) VALUES
(1, 1, '2024-09-02', '2024-09-08', '2024-2025', 1, NOW(), NOW()),
(2, 2, '2024-09-09', '2024-09-15', '2024-2025', 1, NOW(), NOW()),
(3, 3, '2024-09-16', '2024-09-22', '2024-2025', 1, NOW(), NOW()),
(4, 4, '2024-09-23', '2024-09-29', '2024-2025', 1, NOW(), NOW()),
(5, 5, '2024-09-30', '2024-10-06', '2024-2025', 1, NOW(), NOW());

SET FOREIGN_KEY_CHECKS = 1;
