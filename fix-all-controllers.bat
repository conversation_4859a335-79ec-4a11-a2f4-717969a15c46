@echo off
echo ========================================
echo Smart Scheduler Controller Fix Script
echo ========================================

echo.
echo Setting up Java 17 environment...
set "JAVA_HOME=C:\Program Files\Java\jdk-17"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo.
echo Fixing ResponseEntity type inference issues in all controllers...

echo.
echo Step 1: Fixing remaining controllers with PowerShell...

powershell -Command "& {
    $controllers = @(
        'src/main/java/com/smartscheduler/controller/CampusController.java',
        'src/main/java/com/smartscheduler/controller/CourseController.java',
        'src/main/java/com/smartscheduler/controller/MajorController.java',
        'src/main/java/com/smartscheduler/controller/RoomController.java',
        'src/main/java/com/smartscheduler/controller/ScheduleController.java',
        'src/main/java/com/smartscheduler/controller/TeacherWorkloadController.java'
    )
    
    foreach ($controller in $controllers) {
        if (Test-Path $controller) {
            Write-Host \"Fixing: $controller\"
            $content = Get-Content $controller -Raw
            
            # Fix new ResponseEntity<>(..., HttpStatus.OK) patterns
            $content = $content -replace 'new ResponseEntity<>\(([^,]+), HttpStatus\.OK\)', 'ResponseEntity.ok($1)'
            
            # Fix new ResponseEntity<>(..., HttpStatus.CREATED) patterns  
            $content = $content -replace 'new ResponseEntity<>\(([^,]+), HttpStatus\.CREATED\)', 'ResponseEntity.status(HttpStatus.CREATED).body($1)'
            
            # Fix new ResponseEntity<>(..., HttpStatus.NOT_FOUND) patterns
            $content = $content -replace 'new ResponseEntity<>\(([^,]+), HttpStatus\.NOT_FOUND\)', 'ResponseEntity.status(HttpStatus.NOT_FOUND).body($1)'
            
            # Fix map/orElse patterns with type inference issues
            $content = $content -replace '\.map\(([^)]+) -> new ResponseEntity<>\(\1, HttpStatus\.OK\)\)', '.<ResponseEntity<?>>map($1 -> ResponseEntity.ok($1))'
            
            # Fix orElse patterns
            $content = $content -replace '\.orElse\(new ResponseEntity<>\(new MessageResponse\(([^)]+)\), HttpStatus\.NOT_FOUND\)\)', '.orElse(ResponseEntity.status(HttpStatus.NOT_FOUND).body(new MessageResponse($1)))'
            
            Set-Content $controller $content -NoNewline
        }
    }
}"

echo.
echo Step 2: Testing build...
mvn clean compile -DskipTests

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ALL CONTROLLERS FIXED SUCCESSFULLY!
    echo ========================================
    echo.
    echo The ResponseEntity type inference issues have been resolved.
    echo You can now build and run the application.
) else (
    echo.
    echo ========================================
    echo SOME ISSUES REMAIN
    echo ========================================
    echo.
    echo Please check the error messages above for any remaining issues.
)

echo.
echo Press any key to continue...
pause > nul
