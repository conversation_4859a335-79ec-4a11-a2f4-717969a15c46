@echo off
echo ========================================
echo Smart Scheduler Build Fix Script
echo ========================================

echo.
echo Step 1: Setting up Java 17 environment...
set "JAVA_HOME=C:\Program Files\Java\jdk-17"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo JAVA_HOME: %JAVA_HOME%
echo.
echo Java version:
java -version

echo.
echo Maven version:
mvn -version

echo.
echo Step 2: Cleaning previous builds...
mvn clean

echo.
echo Step 3: Compiling with Java 17...
mvn compile -DskipTests

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo The build issue has been resolved.
    echo You can now run the application using:
    echo   mvn spring-boot:run
    echo.
) else (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo.
    echo Please check the error messages above.
)

echo.
echo Press any key to continue...
pause > nul
